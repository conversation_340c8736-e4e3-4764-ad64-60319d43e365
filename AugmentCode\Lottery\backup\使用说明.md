# 彩票数据分析增强版程序 - 使用说明

## 程序概述

本程序是基于Lottery_Final.py开发的增强版彩票分析程序，提供了详细的历史数据分析功能，包括奇偶比、质合比、大小比、和值走势等多维度分析。

## 主要文件

- `lottery_analysis_enhanced.py` - 主程序文件
- `test_enhanced_analysis.py` - 测试程序
- `demo_enhanced_analysis.py` - 演示程序
- `README_enhanced_analysis.md` - 详细说明文档
- `lottery_data_all.xlsx` - 数据文件（需要包含SSQ_data_all和DLT_data_all工作表）

## 快速开始

### 1. 运行演示程序（推荐）
```bash
python demo_enhanced_analysis.py
```
这将展示程序的主要功能，包括单期分析和连续分析的演示。

### 2. 运行测试程序
```bash
python test_enhanced_analysis.py
```
这将测试程序的核心功能是否正常工作。

### 3. 运行完整交互式程序
```bash
python lottery_analysis_enhanced.py
```
这将启动完整的交互式程序，按照提示进行操作。

## 程序功能

### 1. 彩票类型支持
- **SSQ (双色球)**：红球1-33，蓝球1-16
- **DLT (大乐透)**：红球1-35，蓝球1-12

### 2. 历史数据统计分析

#### 奇偶比分析
- 统计红球和蓝球的奇偶比分布
- 显示各种奇偶比组合的出现次数和占比
- 例如：SSQ红球的6:0、5:1、4:2、3:3、2:4、1:5、0:6占比

#### 质合比分析
- 统计红球和蓝球的质数与合数比分布
- 质数：2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31等
- 显示各种质合比组合的出现次数和占比

#### 大小比分析
- **SSQ**：红球大于16、蓝球大于8为大球
- **DLT**：红球大于17、蓝球大于6为大球
- 统计大小球比例分布

#### 和值走势分析
- 按每10个数为一个区间统计和值分布
- 红球和值区间分布
- 蓝球和值分布（单个蓝球时和值为号码本身）

### 3. 单期分析功能

#### a. 历史出现概率分布
- 统计所有红球和蓝球的历史出现概率
- 按概率从大到小排序
- 显示分析期号码在概率排序中的位置
- 输出红球和蓝球的最后排序位置

#### b. 跟随性概率分布
- 基于上一期号码计算多条件贝叶斯预测概率
- 按概率从大到小排序
- 显示分析期号码在跟随性概率排序中的位置
- 输出红球和蓝球的最后排序位置

### 4. 连续分析功能
- 从指定期号开始连续分析
- 最多分析500期
- 不显示详细的概率排序，只显示位置信息
- 到达数据末尾时自动保存结果
- 自动保存分析结果到Excel文件
- 包含详细的分析数据和汇总统计

## 使用流程

### 交互式程序使用流程

1. **选择彩票类型**
   - 输入1选择SSQ（双色球）
   - 输入2选择DLT（大乐透）

2. **选择操作模式**
   - 输入1选择预测模式（暂未实现）
   - 输入2选择分析模式

3. **选择分析模式**
   - 输入1选择单次分析
   - 输入2选择连续分析

4. **输入期号**
   - 单次分析：输入要分析的期号（如25001）
   - 连续分析：输入开始分析的期号

5. **查看结果**
   - 单次分析：在控制台查看详细分析结果
   - 连续分析：结果保存到Excel文件

## 输出结果说明

### 历史数据统计输出（只显示前两项，按百分比从大到小排序）
```
【SSQ奇偶比分析】
红球奇偶比分布:
  3:3 - 1173次 (35.42%)
  4:2 - 810次 (24.46%)
蓝球奇偶比分布:
  1:0 - 1678次 (50.66%)
  0:1 - 1634次 (49.34%)
```

### 单期分析输出（显示全部概率分布）
```
第25001期实际号码：
红球：[2, 9, 15, 16, 29, 32]
蓝球：[14]

【a. 历史出现概率分布】
红球历史概率排序（从大到小）：
  1. 号码 26: 3.2911%
  2. 号码 17: 3.2810%
  3. 号码 30: 3.2709%
  ...（显示全部33个红球）
  33. 号码 33: 2.6671%
蓝球历史概率排序（从大到小）：
  1. 号码  9: 7.3068%
  2. 号码 11: 6.9444%
  ...（显示全部16个蓝球）
  16. 号码  4: 5.1329%

【b. 跟随性概率分布（基于第25000期）】
红球跟随性概率排序（从大到小）：
  1. 号码 26: 0.6243%
  2. 号码 22: 0.6159%
  ...（显示全部33个红球）
  33. 号码 15: 0.4964%
蓝球跟随性概率排序（从大到小）：
  1. 号码  3: 0.7315%
  2. 号码 11: 0.7315%
  ...（显示全部16个蓝球）
  16. 号码 12: 0.0610%

第25001期红球在历史概率排序中的位置：[18, 28, 32, 22, 26, 6]
红球最后排序位置：32
第25001期蓝球在历史概率排序中的位置：[3]
蓝球最后排序位置：3
```

### Excel输出文件
连续分析结果会自动保存到Excel文件，包含：
- 分析序号
- 分析期号
- 红球号码（格式：01+08+09+23+24+30）
- 蓝球号码（格式：08）
- 红球历史概率位置
- 红球历史概率最后位置
- 蓝球历史概率位置
- 蓝球历史概率最后位置
- 红球跟随性概率位置
- 红球跟随性概率最后位置
- 蓝球跟随性概率位置
- 蓝球跟随性概率最后位置

文件名格式：`{彩票类型}_连续分析结果_{时间戳}.xlsx`

## 注意事项

1. **数据文件要求**
   - 确保`lottery_data_all.xlsx`文件存在
   - 文件必须包含`SSQ_data_all`和`DLT_data_all`工作表
   - 数据格式必须正确

2. **期号输入要求**
   - 输入的期号必须在数据范围内
   - 分析需要足够的历史数据（至少10期）

3. **程序运行环境**
   - Python 3.6+
   - 自动安装依赖库：pandas, numpy, openpyxl, xlsxwriter

## 扩展性

程序采用模块化设计，便于后续添加新的分析功能：
- 可以轻松添加新的分析模块
- 可以修改现有分析算法
- 可以扩展预测功能

## 技术支持

如果遇到问题，请检查：
1. 数据文件是否存在且格式正确
2. Python环境是否正确安装
3. 依赖库是否正确安装
4. 输入的期号是否在有效范围内
