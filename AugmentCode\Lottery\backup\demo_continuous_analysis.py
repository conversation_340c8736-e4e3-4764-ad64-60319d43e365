#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票连续分析程序演示
展示如何使用连续分析功能
"""

from lottery_continuous_analysis import LotteryContinuousAnalyzer
import pandas as pd
import numpy as np
import os

def create_demo_data():
    """
    创建演示数据
    """
    print("创建演示数据...")
    
    # 创建更多的SSQ演示数据（200期）
    ssq_data = []
    np.random.seed(42)  # 设置随机种子以获得可重复的结果
    
    for i in range(200):
        period = 25001 + i
        # 生成随机红球号码（1-33，6个不重复）
        red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
        # 生成随机蓝球号码（1-16，1个）
        blue_ball = np.random.choice(range(1, 17), 1)[0]
        
        row = [period] + list(red_balls) + [blue_ball]
        ssq_data.append(row)
    
    # 创建DLT演示数据（200期）
    dlt_data = []
    for i in range(200):
        period = 25001 + i
        # 生成随机红球号码（1-35，5个不重复）
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        # 生成随机蓝球号码（1-12，2个不重复）
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        row = [period] + list(red_balls) + list(blue_balls)
        dlt_data.append(row)
    
    # 创建Excel文件
    with pd.ExcelWriter('demo_lottery_data.xlsx') as writer:
        # SSQ数据
        ssq_df = pd.DataFrame(ssq_data, columns=['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球'])
        # 添加额外列以匹配原始格式（A列 + I-O列）
        ssq_full = pd.DataFrame()
        ssq_full['A'] = ssq_df['期号']
        for i in range(7):  # B-H列填充空值
            ssq_full[f'Col{i+2}'] = 0
        ssq_full['I'] = ssq_df['红球1']
        ssq_full['J'] = ssq_df['红球2']
        ssq_full['K'] = ssq_df['红球3']
        ssq_full['L'] = ssq_df['红球4']
        ssq_full['M'] = ssq_df['红球5']
        ssq_full['N'] = ssq_df['红球6']
        ssq_full['O'] = ssq_df['蓝球']
        ssq_full.to_excel(writer, sheet_name='SSQ_data_all', index=False)
        
        # DLT数据
        dlt_df = pd.DataFrame(dlt_data, columns=['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2'])
        # 添加额外列以匹配原始格式（A列 + H-N列）
        dlt_full = pd.DataFrame()
        dlt_full['A'] = dlt_df['期号']
        for i in range(6):  # B-G列填充空值
            dlt_full[f'Col{i+2}'] = 0
        dlt_full['H'] = dlt_df['红球1']
        dlt_full['I'] = dlt_df['红球2']
        dlt_full['J'] = dlt_df['红球3']
        dlt_full['K'] = dlt_df['红球4']
        dlt_full['L'] = dlt_df['红球5']
        dlt_full['M'] = dlt_df['蓝球1']
        dlt_full['N'] = dlt_df['蓝球2']
        dlt_full.to_excel(writer, sheet_name='DLT_data_all', index=False)
    
    print("演示数据创建完成：demo_lottery_data.xlsx")
    print(f"SSQ数据：期号 {ssq_data[0][0]} - {ssq_data[-1][0]}，共 {len(ssq_data)} 期")
    print(f"DLT数据：期号 {dlt_data[0][0]} - {dlt_data[-1][0]}，共 {len(dlt_data)} 期")

def demo_ssq_analysis():
    """
    演示SSQ连续分析
    """
    print("\n" + "=" * 60)
    print("演示SSQ（双色球）连续分析")
    print("=" * 60)
    
    # 创建分析器实例
    analyzer = LotteryContinuousAnalyzer("demo_lottery_data.xlsx")
    
    # 设置为SSQ类型
    analyzer.lottery_type = "SSQ"
    print("已选择：双色球 (SSQ)")
    
    # 读取数据
    if not analyzer.read_and_sort_data():
        print("读取数据失败")
        return False
    
    # 模拟用户输入：从25051期开始分析（确保有足够的前期数据）
    start_period = 25051
    start_row = None
    for i, row in analyzer.data.iterrows():
        if row.iloc[0] == start_period:
            start_row = i
            break
    
    if start_row is None:
        print(f"未找到期号 {start_period}")
        return False
    
    print(f"开始连续分析，从第 {start_period} 期开始...")
    
    # 限制分析期数为10期（演示用）
    analyzer.analysis_results = []
    analyzed_count = 0
    
    for i in range(10):  # 只分析10期作为演示
        current_row = start_row + i
        
        if current_row >= len(analyzer.data):
            break
            
        current_period = analyzer.data.iloc[current_row, 0]
        
        # 使用当前期号之前的数据作为训练数据
        train_data = analyzer.data.iloc[:current_row].copy()
        
        if len(train_data) < 10:
            continue
        
        # 获取当前期号的实际号码
        config = analyzer.get_lottery_config()
        current_numbers = analyzer.data.iloc[current_row, 1:1+config['red_count']+config['blue_count']].tolist()
        
        # 获取上一期号码
        prev_numbers = analyzer.data.iloc[current_row-1, 1:1+config['red_count']+config['blue_count']].tolist()
        
        print(f"正在分析第 {current_period} 期... ({analyzed_count + 1}/10)")
        
        # 执行分析模块
        analysis_result = {
            'period': current_period,
            'actual_numbers': current_numbers,
            'prev_numbers': prev_numbers,
            'modules': []
        }
        
        # 模块1：历史出现概率分布分析
        hist_analysis = analyzer.analyze_historical_probability(train_data, current_period, current_numbers)
        analysis_result['modules'].append(hist_analysis)
        
        # 模块2：跟随性概率分布分析
        follow_analysis = analyzer.analyze_follow_probability(train_data, current_period, current_numbers, prev_numbers)
        analysis_result['modules'].append(follow_analysis)
        
        # 保存分析结果
        analyzer.analysis_results.append(analysis_result)
        analyzed_count += 1
    
    print(f"\n连续分析完成！共分析了 {analyzed_count} 期")
    
    # 保存结果到Word文档
    if analyzer.save_results_to_word():
        print("演示分析结果已保存到Word文档")
    
    return True

def demo_analysis_results():
    """
    展示分析结果示例
    """
    print("\n" + "=" * 60)
    print("分析结果示例")
    print("=" * 60)
    
    # 创建分析器实例
    analyzer = LotteryContinuousAnalyzer("demo_lottery_data.xlsx")
    analyzer.lottery_type = "SSQ"
    
    if not analyzer.read_and_sort_data():
        return False
    
    # 使用前50期作为训练数据，分析第51期
    train_data = analyzer.data.iloc[:50].copy()
    target_period = analyzer.data.iloc[50, 0]
    target_numbers = analyzer.data.iloc[50, 1:8].tolist()
    prev_numbers = analyzer.data.iloc[49, 1:8].tolist()
    
    print(f"分析期号：{target_period}")
    print(f"实际号码：红球 {target_numbers[:6]}，蓝球 {target_numbers[6:]}")
    print(f"上期号码：红球 {prev_numbers[:6]}，蓝球 {prev_numbers[6:]}")
    
    # 历史概率分析
    hist_result = analyzer.analyze_historical_probability(train_data, target_period, target_numbers)
    print(f"\n历史概率分析：")
    print(f"红球置信区间：[{hist_result['red_confidence']['lower_bound']*100:.4f}%, {hist_result['red_confidence']['upper_bound']*100:.4f}%]")
    print(f"蓝球置信区间：[{hist_result['blue_confidence']['lower_bound']*100:.4f}%, {hist_result['blue_confidence']['upper_bound']*100:.4f}%]")
    
    print("目标期红球概率分析：")
    for ball, analysis in hist_result['target_red_analysis'].items():
        print(f"  号码 {ball:2d}: {analysis['percentage']:.4f}% - {analysis['confidence_analysis']}")
    
    print("目标期蓝球概率分析：")
    for ball, analysis in hist_result['target_blue_analysis'].items():
        print(f"  号码 {ball:2d}: {analysis['percentage']:.4f}% - {analysis['confidence_analysis']}")
    
    # 跟随性概率分析
    follow_result = analyzer.analyze_follow_probability(train_data, target_period, target_numbers, prev_numbers)
    print(f"\n跟随性概率分析：")
    print(f"红球置信区间：[{follow_result['red_confidence']['lower_bound']*100:.4f}%, {follow_result['red_confidence']['upper_bound']*100:.4f}%]")
    print(f"蓝球置信区间：[{follow_result['blue_confidence']['lower_bound']*100:.4f}%, {follow_result['blue_confidence']['upper_bound']*100:.4f}%]")
    
    print("目标期红球跟随性概率分析：")
    for ball, analysis in follow_result['target_red_analysis'].items():
        print(f"  号码 {ball:2d}: {analysis['percentage']:.4f}% - {analysis['confidence_analysis']}")
    
    print("目标期蓝球跟随性概率分析：")
    for ball, analysis in follow_result['target_blue_analysis'].items():
        print(f"  号码 {ball:2d}: {analysis['percentage']:.4f}% - {analysis['confidence_analysis']}")

def main():
    """
    主演示函数
    """
    print("=" * 60)
    print("彩票连续分析程序演示")
    print("=" * 60)
    
    # 创建演示数据
    create_demo_data()
    
    # 展示分析结果示例
    demo_analysis_results()
    
    # 演示SSQ连续分析
    demo_ssq_analysis()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    print("要运行完整的连续分析程序，请执行：")
    print("python lottery_continuous_analysis.py")
    print("\n生成的文件：")
    print("- demo_lottery_data.xlsx：演示数据文件")
    print("- lottery_continuous_analysis_SSQ_*.docx：分析结果Word文档")

if __name__ == "__main__":
    main()
