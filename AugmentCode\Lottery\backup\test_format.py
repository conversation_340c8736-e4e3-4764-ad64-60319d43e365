#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试号码格式化功能
"""

import numpy as np

def test_number_formatting():
    """测试号码格式化功能"""
    
    # 模拟预测结果（包含numpy类型）
    ssq_predicted = [np.int64(3), np.int64(7), np.int64(12), np.int64(18), np.int64(25), np.int64(31), np.int64(8)]
    dlt_predicted = [np.int64(5), np.int64(11), np.int64(17), np.int64(23), np.int64(29), np.int64(3), np.int64(9)]
    
    print("原始数据格式:")
    print(f"SSQ预测号码: {ssq_predicted}")
    print(f"DLT预测号码: {dlt_predicted}")
    print(f"数据类型: {type(ssq_predicted[0])}")
    
    print("\n转换为整数后:")
    
    # SSQ格式化
    ssq_nums = [int(x) for x in ssq_predicted]
    ssq_display = f"{ssq_nums[:6]} + [{ssq_nums[6]}]"
    print(f"SSQ显示格式: {ssq_display}")
    
    # DLT格式化
    dlt_nums = [int(x) for x in dlt_predicted]
    dlt_display = f"{dlt_nums[:5]} + {dlt_nums[5:7]}"
    print(f"DLT显示格式: {dlt_display}")
    
    print(f"\n转换后数据类型: {type(ssq_nums[0])}")

if __name__ == "__main__":
    test_number_formatting()
