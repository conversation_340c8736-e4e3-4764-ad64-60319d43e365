#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票连续分析程序
基于Lottery_Final.py的核心功能，实现连续分析功能：
1. 询问用户选择SSQ还是DLT
2. 询问用户从哪一期号开始连续分析
3. 分析内容包括：
   - 历史出现概率分布
   - 跟随性概率分布
   - 置信区间分析
4. 模块化设计，便于后续修改
5. 将结果保存在Word文档中
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime
import threading
import sys
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os
import scipy.stats as stats

class LotteryContinuousAnalyzer:
    """彩票连续分析器类"""
    
    def __init__(self, file_path="lottery_data_all.xlsx"):
        """
        初始化彩票连续分析器

        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_allout = None
        self.dlthistory_allout = None
        self.lottery_type = None  # 彩票类型
        self.data = None  # 当前使用的数据
        self.analysis_results = []  # 存储分析结果
        
    def read_and_sort_data(self):
        """
        根据选择的彩票类型读取相应的Excel表格数据并排序
        """
        print(f"正在读取{self.lottery_type}数据并排序...")
        print("=" * 60)

        try:
            if self.lottery_type == "SSQ":
                # 读取双色球数据
                print("正在读取双色球数据...")
                ssq_data = pd.read_excel(self.file_path, sheet_name="SSQ_data_all")

                # 提取A列、I列至O列数据（共8列）
                # A列是第0列，I列至O列是第8-14列
                self.ssqhistory_allout = ssq_data.iloc[:, [0] + list(range(8, 15))].copy()

                # 清理和排序SSQ数据
                dataset = self.ssqhistory_allout
                dataset_name = "双色球(SSQ)"
                self.data = dataset

            else:  # DLT
                # 读取大乐透数据
                print("正在读取大乐透数据...")
                dlt_data = pd.read_excel(self.file_path, sheet_name="DLT_data_all")

                # 提取A列、H列至N列数据（共8列）
                # A列是第0列，H列至N列是第7-13列
                self.dlthistory_allout = dlt_data.iloc[:, [0] + list(range(7, 14))].copy()

                # 清理和排序DLT数据
                dataset = self.dlthistory_allout
                dataset_name = "大乐透(DLT)"
                self.data = dataset

            print(f"处理 {dataset_name} 数据...")

            # 删除包含NaN的行
            self.data.dropna(inplace=True)

            # 按第一列（NO列）从小到大排序
            self.data.sort_values(by=self.data.columns[0], inplace=True)

            # 重置索引
            self.data.reset_index(drop=True, inplace=True)

            # 确保数据类型一致性
            try:
                # 第一列是期号，应该是整数类型
                self.data.iloc[:, 0] = self.data.iloc[:, 0].astype(int)

                # 其他列是彩票号码，也应该是整数类型
                for col in range(1, self.data.shape[1]):
                    self.data.iloc[:, col] = self.data.iloc[:, col].astype(int)
            except ValueError as e:
                print(f"警告: 转换 {dataset_name} 的数据类型时出错: {e}")
                # 使用更安全的方法
                for col in range(self.data.shape[1]):
                    self.data.iloc[:, col] = self.data.iloc[:, col].fillna(0).astype(int)

            # 打印数据信息
            print(f"\n{dataset_name} 数据信息:")
            print(f"行数: {self.data.shape[0]}, 列数: {self.data.shape[1]}")
            print(f"期号范围: {self.data.iloc[0, 0]} - {self.data.iloc[-1, 0]}")

            print(f"\n{dataset_name}数据读取和排序成功！")
            return True

        except FileNotFoundError:
            print(f"错误: 找不到文件 '{self.file_path}'，请确保文件存在。")
            return False
        except Exception as e:
            print(f"错误: {str(e)}")
            return False

    def get_user_input(self, prompt):
        """
        获取用户输入

        Args:
            prompt: 提示信息

        Returns:
            用户输入
        """
        try:
            user_input = input(prompt)
            return user_input.strip()
        except KeyboardInterrupt:
            print("\n用户中断程序")
            return ""

    def select_lottery_type(self):
        """
        选择彩票类型
        """
        print("=" * 60)
        print("选择彩票类型")
        print("=" * 60)

        print("\n请选择彩票类型:")
        print("1. SSQ (双色球)")
        print("2. DLT (大乐透)")

        lottery_choice = self.get_user_input("请输入选择 (1 或 2): ")

        if lottery_choice == "2":
            self.lottery_type = "DLT"
            print("您选择了：大乐透 (DLT)")
        else:
            self.lottery_type = "SSQ"
            print("您选择了：双色球 (SSQ)")

        print("\n彩票类型选择成功！")
        return True

    def get_lottery_config(self):
        """
        获取彩票配置信息
        
        Returns:
            dict: 包含红球列、蓝球列、红球范围、蓝球范围的配置信息
        """
        if self.lottery_type == "SSQ":
            return {
                'red_columns': range(1, 7),  # 第2-7列是红球
                'blue_columns': [7],  # 第8列是蓝球
                'red_range': (1, 33),
                'blue_range': (1, 16),
                'red_count': 6,
                'blue_count': 1
            }
        else:  # DLT
            return {
                'red_columns': range(1, 6),  # 第2-6列是红球
                'blue_columns': [6, 7],  # 第7-8列是蓝球
                'red_range': (1, 35),
                'blue_range': (1, 12),
                'red_count': 5,
                'blue_count': 2
            }

    def calculate_ball_statistics(self, df, columns, min_ball, max_ball):
        """
        统计球号出现次数和概率

        Args:
            df: 数据集
            columns: 要统计的列索引列表
            min_ball: 最小球号
            max_ball: 最大球号

        Returns:
            times: 各球号出现次数的字典
            probs: 各球号出现概率的字典
        """
        # 创建一个字典，用于存储每个球号的出现次数
        times = {i: 0 for i in range(min_ball, max_ball + 1)}

        # 将所有指定列的数据合并为一个numpy数组
        all_balls = np.array([])
        for col in columns:
            all_balls = np.append(all_balls, df.iloc[:, col].values)

        # 统计每个球号的出现次数
        for ball in all_balls:
            if min_ball <= ball <= max_ball:
                times[int(ball)] += 1

        # 计算总次数
        total_count = sum(times.values())

        # 计算每个球号的出现概率
        probs = {ball: count / total_count if total_count > 0 else 0 for ball, count in times.items()}

        return times, probs

    def calculate_follow_statistics(self, df, columns, min_ball, max_ball):
        """
        计算球号的跟随性统计

        Args:
            df: 数据集
            columns: 要统计的列索引列表
            min_ball: 最小球号
            max_ball: 最大球号

        Returns:
            follow_time: 跟随次数矩阵
            follow_prob: 跟随概率矩阵
        """
        # 创建跟随次数矩阵和概率矩阵
        num_balls = max_ball - min_ball + 1
        follow_time = np.zeros((num_balls, num_balls), dtype=int)
        follow_prob = np.zeros((num_balls, num_balls), dtype=float)

        # 遍历数据集中的每一行（除了最后一行）
        for i in range(len(df) - 1):
            # 获取当前行和下一行的球号
            current_row_balls = set()
            next_row_balls = set()

            # 收集当前行的所有球号
            for col in columns:
                ball = df.iloc[i, col]
                if min_ball <= ball <= max_ball:
                    current_row_balls.add(int(ball))

            # 收集下一行的所有球号
            for col in columns:
                ball = df.iloc[i+1, col]
                if min_ball <= ball <= max_ball:
                    next_row_balls.add(int(ball))

            # 更新跟随次数矩阵
            for current_ball in current_row_balls:
                for next_ball in next_row_balls:
                    # 矩阵索引从0开始，所以需要减去min_ball
                    follow_time[next_ball - min_ball, current_ball - min_ball] += 1

        # 计算跟随概率矩阵
        for col in range(num_balls):
            col_sum = np.sum(follow_time[:, col])
            if col_sum > 0:
                follow_prob[:, col] = follow_time[:, col] / col_sum

        return follow_time, follow_prob

    def calculate_multi_bayesian_probs_for_data(self, data, target_period_numbers):
        """
        为指定数据计算多条件贝叶斯概率（跟随性概率）

        Args:
            data: 训练数据
            target_period_numbers: 目标期号的上一期号码

        Returns:
            red_follow_probs: 红球跟随性概率字典
            blue_follow_probs: 蓝球跟随性概率字典
        """
        config = self.get_lottery_config()

        # 统计红球和蓝球的出现次数和概率
        _, red_probs = self.calculate_ball_statistics(
            data, config['red_columns'], config['red_range'][0], config['red_range'][1])
        _, blue_probs = self.calculate_ball_statistics(
            data, config['blue_columns'], config['blue_range'][0], config['blue_range'][1])

        # 统计跟随性
        _, red_follow_prob = self.calculate_follow_statistics(
            data, config['red_columns'], config['red_range'][0], config['red_range'][1])
        _, blue_follow_prob = self.calculate_follow_statistics(
            data, config['blue_columns'], config['blue_range'][0], config['blue_range'][1])

        # 红球多条件贝叶斯概率
        red_follow_probs = {}
        red_probs_list = [red_probs[i] for i in range(config['red_range'][0], config['red_range'][1] + 1)]
        for i in range(config['red_range'][1] - config['red_range'][0] + 1):
            prob_sum = 0
            for j, col in enumerate(config['red_columns']):
                last_ball = target_period_numbers[col - 1]  # 获取上一期对应位置的球号
                if config['red_range'][0] <= last_ball <= config['red_range'][1]:
                    prob_sum += red_follow_prob[i, last_ball - config['red_range'][0]] * red_probs_list[last_ball - config['red_range'][0]]
            red_follow_probs[i + config['red_range'][0]] = prob_sum

        # 蓝球多条件贝叶斯概率
        blue_follow_probs = {}
        blue_probs_list = [blue_probs[i] for i in range(config['blue_range'][0], config['blue_range'][1] + 1)]
        for i in range(config['blue_range'][1] - config['blue_range'][0] + 1):
            prob_sum = 0
            for j, col in enumerate(config['blue_columns']):
                last_ball = target_period_numbers[col - 1]  # 获取上一期对应位置的球号
                if config['blue_range'][0] <= last_ball <= config['blue_range'][1]:
                    prob_sum += blue_follow_prob[i, last_ball - config['blue_range'][0]] * blue_probs_list[last_ball - config['blue_range'][0]]
            blue_follow_probs[i + config['blue_range'][0]] = prob_sum

        return red_follow_probs, blue_follow_probs

    def calculate_confidence_interval(self, probabilities, confidence_level=0.95):
        """
        计算概率分布的置信区间

        Args:
            probabilities: 概率字典
            confidence_level: 置信水平，默认95%

        Returns:
            dict: 包含置信区间信息的字典
        """
        prob_values = list(probabilities.values())
        prob_array = np.array(prob_values)

        # 计算统计量
        mean_prob = np.mean(prob_array)
        std_prob = np.std(prob_array)

        # 计算置信区间
        alpha = 1 - confidence_level
        z_score = stats.norm.ppf(1 - alpha/2)
        margin_error = z_score * std_prob / np.sqrt(len(prob_array))

        lower_bound = mean_prob - margin_error
        upper_bound = mean_prob + margin_error

        return {
            'mean': mean_prob,
            'std': std_prob,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound,
            'confidence_level': confidence_level
        }

    def analyze_ball_in_confidence_interval(self, ball_prob, confidence_info):
        """
        分析球号概率在置信区间中的位置

        Args:
            ball_prob: 球号的概率
            confidence_info: 置信区间信息

        Returns:
            str: 分析结果描述
        """
        if ball_prob < confidence_info['lower_bound']:
            return f"低于{confidence_info['confidence_level']*100:.0f}%置信区间下界"
        elif ball_prob > confidence_info['upper_bound']:
            return f"高于{confidence_info['confidence_level']*100:.0f}%置信区间上界"
        else:
            return f"在{confidence_info['confidence_level']*100:.0f}%置信区间内"

    # ==================== 分析模块 ====================

    def analyze_historical_probability(self, train_data, target_period, target_numbers):
        """
        分析模块1：历史出现概率分布分析

        Args:
            train_data: 训练数据
            target_period: 目标期号
            target_numbers: 目标期号的实际号码

        Returns:
            dict: 分析结果
        """
        config = self.get_lottery_config()

        # 计算红球和蓝球的历史出现概率
        _, red_probs = self.calculate_ball_statistics(
            train_data, config['red_columns'], config['red_range'][0], config['red_range'][1])
        _, blue_probs = self.calculate_ball_statistics(
            train_data, config['blue_columns'], config['blue_range'][0], config['blue_range'][1])

        # 计算置信区间
        red_confidence = self.calculate_confidence_interval(red_probs)
        blue_confidence = self.calculate_confidence_interval(blue_probs)

        # 分析目标期号的红球和蓝球概率
        target_red_balls = target_numbers[:config['red_count']]
        target_blue_balls = target_numbers[config['red_count']:]

        red_analysis = {}
        for ball in target_red_balls:
            ball_prob = red_probs.get(ball, 0)
            analysis = self.analyze_ball_in_confidence_interval(ball_prob, red_confidence)
            red_analysis[ball] = {
                'probability': ball_prob,
                'percentage': ball_prob * 100,
                'confidence_analysis': analysis
            }

        blue_analysis = {}
        for ball in target_blue_balls:
            ball_prob = blue_probs.get(ball, 0)
            analysis = self.analyze_ball_in_confidence_interval(ball_prob, blue_confidence)
            blue_analysis[ball] = {
                'probability': ball_prob,
                'percentage': ball_prob * 100,
                'confidence_analysis': analysis
            }

        return {
            'module_name': '历史出现概率分布分析',
            'target_period': target_period,
            'red_probs_all': {k: v*100 for k, v in red_probs.items()},  # 转换为百分比
            'blue_probs_all': {k: v*100 for k, v in blue_probs.items()},  # 转换为百分比
            'red_confidence': red_confidence,
            'blue_confidence': blue_confidence,
            'target_red_analysis': red_analysis,
            'target_blue_analysis': blue_analysis
        }

    def analyze_follow_probability(self, train_data, target_period, target_numbers, prev_period_numbers):
        """
        分析模块2：跟随性概率分布分析

        Args:
            train_data: 训练数据
            target_period: 目标期号
            target_numbers: 目标期号的实际号码
            prev_period_numbers: 目标期号的上一期号码

        Returns:
            dict: 分析结果
        """
        config = self.get_lottery_config()

        # 计算跟随性概率
        red_follow_probs, blue_follow_probs = self.calculate_multi_bayesian_probs_for_data(
            train_data, prev_period_numbers)

        # 计算置信区间
        red_confidence = self.calculate_confidence_interval(red_follow_probs)
        blue_confidence = self.calculate_confidence_interval(blue_follow_probs)

        # 分析目标期号的红球和蓝球跟随性概率
        target_red_balls = target_numbers[:config['red_count']]
        target_blue_balls = target_numbers[config['red_count']:]

        red_analysis = {}
        for ball in target_red_balls:
            ball_prob = red_follow_probs.get(ball, 0)
            analysis = self.analyze_ball_in_confidence_interval(ball_prob, red_confidence)
            red_analysis[ball] = {
                'probability': ball_prob,
                'percentage': ball_prob * 100,
                'confidence_analysis': analysis
            }

        blue_analysis = {}
        for ball in target_blue_balls:
            ball_prob = blue_follow_probs.get(ball, 0)
            analysis = self.analyze_ball_in_confidence_interval(ball_prob, blue_confidence)
            blue_analysis[ball] = {
                'probability': ball_prob,
                'percentage': ball_prob * 100,
                'confidence_analysis': analysis
            }

        return {
            'module_name': '跟随性概率分布分析',
            'target_period': target_period,
            'prev_period_numbers': prev_period_numbers,
            'red_follow_probs_all': {k: v*100 for k, v in red_follow_probs.items()},  # 转换为百分比
            'blue_follow_probs_all': {k: v*100 for k, v in blue_follow_probs.items()},  # 转换为百分比
            'red_confidence': red_confidence,
            'blue_confidence': blue_confidence,
            'target_red_analysis': red_analysis,
            'target_blue_analysis': blue_analysis
        }

    def get_start_period(self):
        """
        获取用户输入的开始分析期号

        Returns:
            int: 开始期号，如果输入错误返回None
        """
        print("\n" + "=" * 60)
        print("设置连续分析参数")
        print("=" * 60)

        # 循环询问用户开始期号，直到输入正确
        while True:
            period_input = self.get_user_input("请输入开始分析的期号（例如：25001）: ")

            if not period_input:
                print("输入错误，请重新输入期号")
                continue

            try:
                start_period = int(period_input)
            except ValueError:
                print("输入错误，请重新输入期号（请输入数字）")
                continue

            # 查找开始期号在数据中的位置
            start_row = None
            for i, row in self.data.iterrows():
                if row.iloc[0] == start_period:
                    start_row = i
                    break

            if start_row is None:
                print(f"输入错误，请重新输入期号（未找到期号 {start_period}）")
                continue

            # 检查是否有足够的前期数据（至少需要10期作为训练数据）
            if start_row < 10:
                print(f"期号 {start_period} 前期数据不足（需要至少10期训练数据），请选择更晚的期号")
                continue

            # 输入正确，跳出循环
            return start_period, start_row

    def continuous_analysis(self):
        """
        连续分析主函数：从指定期号开始连续分析最多500期
        """
        print("\n" + "=" * 60)
        print("开始连续分析")
        print("=" * 60)

        # 获取开始期号
        start_period, start_row = self.get_start_period()

        print(f"开始连续分析，从第 {start_period} 期开始...")

        # 存储分析结果
        self.analysis_results = []

        # 最多分析500期
        analyzed_count = 0
        for i in range(500):
            current_row = start_row + i

            # 检查是否超出数据范围
            if current_row >= len(self.data):
                print(f"已达到数据末尾，共分析了 {analyzed_count} 期")
                break

            current_period = self.data.iloc[current_row, 0]

            # 使用当前期号之前的数据作为训练数据
            train_data = self.data.iloc[:current_row].copy()

            if len(train_data) < 10:
                print(f"第 {current_period} 期训练数据不足，跳过")
                continue

            # 获取当前期号的实际号码
            config = self.get_lottery_config()
            current_numbers = self.data.iloc[current_row, 1:1+config['red_count']+config['blue_count']].tolist()

            # 获取上一期号码（用于跟随性分析）
            prev_numbers = self.data.iloc[current_row-1, 1:1+config['red_count']+config['blue_count']].tolist()

            print(f"正在分析第 {current_period} 期... ({analyzed_count + 1}/500)")

            # 执行分析模块
            analysis_result = {
                'period': current_period,
                'actual_numbers': current_numbers,
                'prev_numbers': prev_numbers,
                'modules': []
            }

            # 模块1：历史出现概率分布分析
            hist_analysis = self.analyze_historical_probability(train_data, current_period, current_numbers)
            analysis_result['modules'].append(hist_analysis)

            # 模块2：跟随性概率分布分析
            follow_analysis = self.analyze_follow_probability(train_data, current_period, current_numbers, prev_numbers)
            analysis_result['modules'].append(follow_analysis)

            # 保存分析结果
            self.analysis_results.append(analysis_result)
            analyzed_count += 1

        print(f"\n连续分析完成！共分析了 {analyzed_count} 期")
        return True

    def save_results_to_word(self):
        """
        将分析结果保存到Word文档
        """
        if not self.analysis_results:
            print("没有分析结果可保存")
            return False

        # 创建Word文档
        doc = Document()

        # 添加标题
        title = doc.add_heading(f'{self.lottery_type}彩票连续分析报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加基本信息
        doc.add_heading('分析概况', level=1)
        doc.add_paragraph(f'彩票类型：{self.lottery_type}')
        doc.add_paragraph(f'分析期数：{len(self.analysis_results)} 期')
        doc.add_paragraph(f'分析时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

        # 为每期分析结果添加内容
        for i, result in enumerate(self.analysis_results):
            # 添加期号标题
            doc.add_heading(f'第 {result["period"]} 期分析', level=1)

            # 添加基本信息
            config = self.get_lottery_config()
            red_balls = result['actual_numbers'][:config['red_count']]
            blue_balls = result['actual_numbers'][config['red_count']:]
            prev_red_balls = result['prev_numbers'][:config['red_count']]
            prev_blue_balls = result['prev_numbers'][config['red_count']:]

            doc.add_paragraph(f'实际号码：红球 {red_balls}，蓝球 {blue_balls}')
            doc.add_paragraph(f'上期号码：红球 {prev_red_balls}，蓝球 {prev_blue_balls}')

            # 为每个分析模块添加内容
            for module in result['modules']:
                doc.add_heading(module['module_name'], level=2)

                if module['module_name'] == '历史出现概率分布分析':
                    self._add_historical_analysis_to_doc(doc, module)
                elif module['module_name'] == '跟随性概率分布分析':
                    self._add_follow_analysis_to_doc(doc, module)

            # 添加分页符（除了最后一期）
            if i < len(self.analysis_results) - 1:
                doc.add_page_break()

        # 保存文档
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"lottery_continuous_analysis_{self.lottery_type}_{timestamp}.docx"
        doc.save(filename)

        print(f"\n分析结果已保存到Word文档：{filename}")
        return True

    def _add_historical_analysis_to_doc(self, doc, module):
        """
        添加历史概率分析内容到Word文档
        """
        # 红球分析
        doc.add_heading('红球历史出现概率分析', level=3)

        # 红球概率分布（只显示前10个最高概率的球号）
        red_probs_sorted = sorted(module['red_probs_all'].items(), key=lambda x: x[1], reverse=True)
        doc.add_paragraph('红球概率分布（前10位）：')
        for i, (ball, prob) in enumerate(red_probs_sorted[:10]):
            doc.add_paragraph(f'  {i+1}. 号码 {ball:2d}: {prob:.4f}%', style='List Number')

        # 红球置信区间
        red_conf = module['red_confidence']
        doc.add_paragraph(f'红球概率置信区间（95%）：[{red_conf["lower_bound"]*100:.4f}%, {red_conf["upper_bound"]*100:.4f}%]')

        # 目标期红球分析
        doc.add_paragraph('目标期红球概率分析：')
        for ball, analysis in module['target_red_analysis'].items():
            doc.add_paragraph(f'  号码 {ball:2d}: {analysis["percentage"]:.4f}% - {analysis["confidence_analysis"]}', style='List Bullet')

        # 蓝球分析
        doc.add_heading('蓝球历史出现概率分析', level=3)

        # 蓝球概率分布
        blue_probs_sorted = sorted(module['blue_probs_all'].items(), key=lambda x: x[1], reverse=True)
        doc.add_paragraph('蓝球概率分布：')
        for i, (ball, prob) in enumerate(blue_probs_sorted):
            doc.add_paragraph(f'  {i+1}. 号码 {ball:2d}: {prob:.4f}%', style='List Number')

        # 蓝球置信区间
        blue_conf = module['blue_confidence']
        doc.add_paragraph(f'蓝球概率置信区间（95%）：[{blue_conf["lower_bound"]*100:.4f}%, {blue_conf["upper_bound"]*100:.4f}%]')

        # 目标期蓝球分析
        doc.add_paragraph('目标期蓝球概率分析：')
        for ball, analysis in module['target_blue_analysis'].items():
            doc.add_paragraph(f'  号码 {ball:2d}: {analysis["percentage"]:.4f}% - {analysis["confidence_analysis"]}', style='List Bullet')

    def _add_follow_analysis_to_doc(self, doc, module):
        """
        添加跟随性概率分析内容到Word文档
        """
        # 基于上期号码信息
        config = self.get_lottery_config()
        prev_red = module['prev_period_numbers'][:config['red_count']]
        prev_blue = module['prev_period_numbers'][config['red_count']:]
        doc.add_paragraph(f'基于上期号码：红球 {prev_red}，蓝球 {prev_blue}')

        # 红球跟随性分析
        doc.add_heading('红球跟随性概率分析', level=3)

        # 红球跟随性概率分布（只显示前10个最高概率的球号）
        red_follow_sorted = sorted(module['red_follow_probs_all'].items(), key=lambda x: x[1], reverse=True)
        doc.add_paragraph('红球跟随性概率分布（前10位）：')
        for i, (ball, prob) in enumerate(red_follow_sorted[:10]):
            doc.add_paragraph(f'  {i+1}. 号码 {ball:2d}: {prob:.4f}%', style='List Number')

        # 红球置信区间
        red_conf = module['red_confidence']
        doc.add_paragraph(f'红球跟随性概率置信区间（95%）：[{red_conf["lower_bound"]*100:.4f}%, {red_conf["upper_bound"]*100:.4f}%]')

        # 目标期红球跟随性分析
        doc.add_paragraph('目标期红球跟随性概率分析：')
        for ball, analysis in module['target_red_analysis'].items():
            doc.add_paragraph(f'  号码 {ball:2d}: {analysis["percentage"]:.4f}% - {analysis["confidence_analysis"]}', style='List Bullet')

        # 蓝球跟随性分析
        doc.add_heading('蓝球跟随性概率分析', level=3)

        # 蓝球跟随性概率分布
        blue_follow_sorted = sorted(module['blue_follow_probs_all'].items(), key=lambda x: x[1], reverse=True)
        doc.add_paragraph('蓝球跟随性概率分布：')
        for i, (ball, prob) in enumerate(blue_follow_sorted):
            doc.add_paragraph(f'  {i+1}. 号码 {ball:2d}: {prob:.4f}%', style='List Number')

        # 蓝球置信区间
        blue_conf = module['blue_confidence']
        doc.add_paragraph(f'蓝球跟随性概率置信区间（95%）：[{blue_conf["lower_bound"]*100:.4f}%, {blue_conf["upper_bound"]*100:.4f}%]')

        # 目标期蓝球跟随性分析
        doc.add_paragraph('目标期蓝球跟随性概率分析：')
        for ball, analysis in module['target_blue_analysis'].items():
            doc.add_paragraph(f'  号码 {ball:2d}: {analysis["percentage"]:.4f}% - {analysis["confidence_analysis"]}', style='List Bullet')

    def run(self):
        """
        运行主程序
        """
        print("=" * 60)
        print("彩票连续分析程序")
        print("=" * 60)

        try:
            # 1. 选择彩票类型
            if not self.select_lottery_type():
                return False

            # 2. 读取和排序数据
            if not self.read_and_sort_data():
                return False

            # 3. 执行连续分析
            if not self.continuous_analysis():
                return False

            # 4. 保存结果到Word文档
            if not self.save_results_to_word():
                return False

            print("\n程序执行完成！")
            return True

        except Exception as e:
            print(f"程序执行出错：{str(e)}")
            return False

def main():
    """
    主函数
    """
    analyzer = LotteryContinuousAnalyzer()
    analyzer.run()

if __name__ == "__main__":
    main()
