#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票数据分析四步程序
按照用户要求的四个步骤进行彩票数据分析：
1. 读取Excel表格数据并排序
2. 指定数据范围、开始行与计算公式
3. 开始计算和比对
4. 将校核结果保存在Excel文件中
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime
import threading
import sys

class LotteryAnalyzer:
    """彩票分析器类"""
    
    def __init__(self, file_path="lottery_data_all.xlsx"):
        """
        初始化彩票分析器

        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_allout = None
        self.dlthistory_allout = None
        self.results = []  # 存储校核结果
        self.lottery_type = None  # 彩票类型
        self.data = None  # 当前使用的数据
        self.operation_mode = None  # 操作模式：prediction/analysis
        self.prediction_mode = None  # 预测模式：two/multi
        self.analysis_mode = None  # 分析模式：single/continuous
        
    def read_and_sort_data(self):
        """
        根据选择的彩票类型读取相应的Excel表格数据并排序
        """
        print(f"正在读取{self.lottery_type}数据并排序...")
        print("=" * 60)

        try:
            if self.lottery_type == "SSQ":
                # 读取双色球数据
                print("正在读取双色球数据...")
                ssq_data = pd.read_excel(self.file_path, sheet_name="SSQ_data_all")

                # 提取A列、I列至O列数据（共8列）
                # A列是第0列，I列至O列是第8-14列
                self.ssqhistory_allout = ssq_data.iloc[:, [0] + list(range(8, 15))].copy()

                # 清理和排序SSQ数据
                dataset = self.ssqhistory_allout
                dataset_name = "双色球(SSQ)"

            else:  # DLT
                # 读取大乐透数据
                print("正在读取大乐透数据...")
                dlt_data = pd.read_excel(self.file_path, sheet_name="DLT_data_all")

                # 提取A列、H列至N列数据（共8列）
                # A列是第0列，H列至N列是第7-13列
                self.dlthistory_allout = dlt_data.iloc[:, [0] + list(range(7, 14))].copy()

                # 清理和排序DLT数据
                dataset = self.dlthistory_allout
                dataset_name = "大乐透(DLT)"

            print(f"处理 {dataset_name} 数据...")

            # 删除包含NaN的行
            dataset.dropna(inplace=True)

            # 按第一列（NO列）从小到大排序
            dataset.sort_values(by=dataset.columns[0], inplace=True)

            # 重置索引
            dataset.reset_index(drop=True, inplace=True)

            # 确保数据类型一致性
            try:
                # 第一列是期号，应该是整数类型
                dataset.iloc[:, 0] = dataset.iloc[:, 0].astype(int)

                # 其他列是彩票号码，也应该是整数类型
                for col in range(1, dataset.shape[1]):
                    dataset.iloc[:, col] = dataset.iloc[:, col].astype(int)
            except ValueError as e:
                print(f"警告: 转换 {dataset_name} 的数据类型时出错: {e}")
                # 使用更安全的方法
                for col in range(dataset.shape[1]):
                    dataset.iloc[:, col] = dataset.iloc[:, col].fillna(0).astype(int)

            # 打印数据信息
            print(f"\n{dataset_name} 数据信息:")
            print(f"行数: {dataset.shape[0]}, 列数: {dataset.shape[1]}")
            print(f"期号范围: {dataset.iloc[0, 0]} - {dataset.iloc[-1, 0]}")

            print(f"\n{dataset_name}数据读取和排序成功！")
            return True

        except FileNotFoundError:
            print(f"错误: 找不到文件 '{self.file_path}'，请确保文件存在。")
            return False
        except Exception as e:
            print(f"错误: {str(e)}")
            return False

    def get_user_input(self, prompt):
        """
        获取用户输入

        Args:
            prompt: 提示信息

        Returns:
            用户输入
        """
        try:
            user_input = input(prompt)
            return user_input.strip()
        except KeyboardInterrupt:
            print("\n用户中断程序")
            return ""

    def step1_select_lottery_type(self):
        """
        第一步：选择彩票类型
        """
        print("=" * 60)
        print("第一步：选择彩票类型")
        print("=" * 60)

        print("\n请选择彩票类型:")
        print("1. SSQ (双色球)")
        print("2. DLT (大乐透)")

        lottery_choice = self.get_user_input("请输入选择 (1 或 2): ")

        if lottery_choice == "2":
            self.lottery_type = "DLT"
            print("您选择了：大乐透 (DLT)")
        else:
            self.lottery_type = "SSQ"
            print("您选择了：双色球 (SSQ)")

        print("\n第一步完成：彩票类型选择成功！")
        return True

    def step2_select_operation_mode(self):
        """
        第二步：选择操作模式
        """
        print("\n" + "=" * 60)
        print("第二步：选择操作模式")
        print("=" * 60)

        print("\n请选择操作模式:")
        print("1. 预测")
        print("2. 分析")

        mode_choice = self.get_user_input("请输入选择 (1 或 2): ")

        if mode_choice == "2":
            self.operation_mode = "analysis"
            print("您选择了：分析模式")
            return self.select_analysis_mode()
        else:
            self.operation_mode = "prediction"
            print("您选择了：预测模式")
            return self.select_prediction_mode()

    def select_prediction_mode(self):
        """
        选择预测模式的子选项
        """
        print("\n请选择预测模式:")
        print("1. 两注号码预测")
        print("2. 多注号码预测")

        prediction_choice = self.get_user_input("请输入选择 (1 或 2): ")

        if prediction_choice == "2":
            self.prediction_mode = "multi"
            print("您选择了：多注号码预测")
        else:
            self.prediction_mode = "two"
            print("您选择了：两注号码预测")

        print("\n第二步完成：操作模式选择成功！")
        return True

    def select_analysis_mode(self):
        """
        选择分析模式的子选项
        """
        print("\n请选择分析模式:")
        print("1. 单次分析")
        print("2. 连续分析")

        analysis_choice = self.get_user_input("请输入选择 (1 或 2): ")

        if analysis_choice == "2":
            self.analysis_mode = "continuous"
            print("您选择了：连续分析")
        else:
            self.analysis_mode = "single"
            print("您选择了：单次分析")

        print("\n第二步完成：操作模式选择成功！")
        return True

    def calculate_ball_statistics(self, df, columns, min_ball, max_ball):
        """
        统计球号出现次数和概率

        Args:
            df: 数据集
            columns: 要统计的列索引列表
            min_ball: 最小球号
            max_ball: 最大球号

        Returns:
            times: 各球号出现次数的字典
            probs: 各球号出现概率的字典
        """
        # 创建一个字典，用于存储每个球号的出现次数
        times = {i: 0 for i in range(min_ball, max_ball + 1)}

        # 将所有指定列的数据合并为一个numpy数组
        all_balls = np.array([])
        for col in columns:
            all_balls = np.append(all_balls, df.iloc[:, col].values)

        # 统计每个球号的出现次数
        for ball in all_balls:
            if min_ball <= ball <= max_ball:
                times[int(ball)] += 1

        # 计算总次数
        total_count = sum(times.values())

        # 计算每个球号的出现概率
        probs = {ball: count / total_count if total_count > 0 else 0 for ball, count in times.items()}

        return times, probs

    def is_prime(self, n):
        """
        判断一个数是否为质数

        Args:
            n: 要判断的数

        Returns:
            bool: 是否为质数
        """
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False
        for i in range(3, int(n**0.5) + 1, 2):
            if n % i == 0:
                return False
        return True

    def calculate_repeat_analysis(self):
        """
        计算重号分析（连续两期的相同号码）

        Returns:
            repeat_stats: 重号统计结果
        """
        repeat_stats = {
            'red_repeats': [],  # 红球重号记录
            'blue_repeats': [],  # 蓝球重号记录
            'red_repeat_count': 0,  # 红球重号总次数
            'blue_repeat_count': 0,  # 蓝球重号总次数
            'red_repeat_numbers': {},  # 各红球号码的重号次数
            'blue_repeat_numbers': {}  # 各蓝球号码的重号次数
        }

        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)

        # 初始化重号次数统计
        for i in range(red_range[0], red_range[1] + 1):
            repeat_stats['red_repeat_numbers'][i] = 0
        for i in range(blue_range[0], blue_range[1] + 1):
            repeat_stats['blue_repeat_numbers'][i] = 0

        # 分析连续两期的重号情况
        for i in range(len(self.data) - 1):
            # 获取当前期和下一期的号码
            current_red = set([self.data.iloc[i, col] for col in red_columns])
            next_red = set([self.data.iloc[i+1, col] for col in red_columns])

            current_blue = set([self.data.iloc[i, col] for col in blue_columns])
            next_blue = set([self.data.iloc[i+1, col] for col in blue_columns])

            # 计算红球重号
            red_repeat = current_red & next_red
            if red_repeat:
                repeat_stats['red_repeats'].append({
                    'period1': self.data.iloc[i, 0],
                    'period2': self.data.iloc[i+1, 0],
                    'repeat_numbers': list(red_repeat),
                    'repeat_count': len(red_repeat)
                })
                repeat_stats['red_repeat_count'] += len(red_repeat)
                for num in red_repeat:
                    repeat_stats['red_repeat_numbers'][num] += 1

            # 计算蓝球重号
            blue_repeat = current_blue & next_blue
            if blue_repeat:
                repeat_stats['blue_repeats'].append({
                    'period1': self.data.iloc[i, 0],
                    'period2': self.data.iloc[i+1, 0],
                    'repeat_numbers': list(blue_repeat),
                    'repeat_count': len(blue_repeat)
                })
                repeat_stats['blue_repeat_count'] += len(blue_repeat)
                for num in blue_repeat:
                    repeat_stats['blue_repeat_numbers'][num] += 1

        return repeat_stats

    def calculate_follow_statistics(self, df, columns, min_ball, max_ball):
        """
        计算球号的跟随性统计

        Args:
            df: 数据集
            columns: 要统计的列索引列表
            min_ball: 最小球号
            max_ball: 最大球号

        Returns:
            follow_time: 跟随次数矩阵
            follow_prob: 跟随概率矩阵
        """
        # 创建跟随次数矩阵和概率矩阵
        num_balls = max_ball - min_ball + 1
        follow_time = np.zeros((num_balls, num_balls), dtype=int)
        follow_prob = np.zeros((num_balls, num_balls), dtype=float)

        # 遍历数据集中的每一行（除了最后一行）
        for i in range(len(df) - 1):
            # 获取当前行和下一行的球号
            current_row_balls = set()
            next_row_balls = set()

            # 收集当前行的所有球号
            for col in columns:
                ball = df.iloc[i, col]
                if min_ball <= ball <= max_ball:
                    current_row_balls.add(int(ball))

            # 收集下一行的所有球号
            for col in columns:
                ball = df.iloc[i+1, col]
                if min_ball <= ball <= max_ball:
                    next_row_balls.add(int(ball))

            # 更新跟随次数矩阵
            for current_ball in current_row_balls:
                for next_ball in next_row_balls:
                    # 矩阵索引从0开始，所以需要减去min_ball
                    follow_time[next_ball - min_ball, current_ball - min_ball] += 1

        # 计算跟随概率矩阵
        for col in range(num_balls):
            col_sum = np.sum(follow_time[:, col])
            if col_sum > 0:
                follow_prob[:, col] = follow_time[:, col] / col_sum

        return follow_time, follow_prob

    def predict_numbers(self, train_data, method="multi_bayesian"):
        """
        根据训练数据预测下一期号码

        Args:
            train_data: 训练数据
            method: 预测方法

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if len(train_data) < 10:  # 需要足够的训练数据
            return None

        if self.lottery_type == "SSQ":
            # 双色球：红球1-33，蓝球1-16
            red_columns = range(1, 7)  # 第2-7列是红球
            blue_columns = [7]  # 第8列是蓝球
            red_range = (1, 33)
            blue_range = (1, 16)
            red_count = 6
            blue_count = 1
        else:  # DLT
            # 大乐透：红球1-35，蓝球1-12
            red_columns = range(1, 6)  # 第2-6列是红球
            blue_columns = [6, 7]  # 第7-8列是蓝球
            red_range = (1, 35)
            blue_range = (1, 12)
            red_count = 5
            blue_count = 2

        # 统计红球和蓝球的出现次数和概率
        _, red_probs = self.calculate_ball_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        _, blue_probs = self.calculate_ball_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 统计跟随性
        _, red_follow_prob = self.calculate_follow_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        _, blue_follow_prob = self.calculate_follow_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 获取最新一期号码
        last_numbers = train_data.iloc[-1, 1:].values

        # 新增的AI预测方法
        if method == "bayesian_ai":
            return self.bayesian_prediction_ai(train_data)
        elif method == "markov":
            return self.markov_chain_prediction(train_data)
        elif method == "ensemble":
            return self.ensemble_prediction(train_data)

        # 原有的贝叶斯方法
        if method == "bayesian":
            # 基于贝叶斯概率预测
            red_prediction_probs = np.array([red_probs[i] for i in range(red_range[0], red_range[1] + 1)])
            blue_prediction_probs = np.array([blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)])

        elif method == "multi_bayesian":
            # 基于多条件贝叶斯概率预测
            red_prediction_probs = np.zeros(red_range[1] - red_range[0] + 1)
            blue_prediction_probs = np.zeros(blue_range[1] - blue_range[0] + 1)

            # 红球多条件贝叶斯概率
            red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
            for i in range(red_range[1] - red_range[0] + 1):
                prob_sum = 0
                for j, col in enumerate(red_columns):
                    last_ball = last_numbers[col - 1]  # 获取最新一期对应位置的球号
                    if red_range[0] <= last_ball <= red_range[1]:
                        prob_sum += red_follow_prob[i, last_ball - red_range[0]] * red_probs_list[last_ball - red_range[0]]
                red_prediction_probs[i] = prob_sum

            # 蓝球多条件贝叶斯概率
            blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
            for i in range(blue_range[1] - blue_range[0] + 1):
                prob_sum = 0
                for j, col in enumerate(blue_columns):
                    last_ball = last_numbers[col - 1]  # 获取最新一期对应位置的球号
                    if blue_range[0] <= last_ball <= blue_range[1]:
                        prob_sum += blue_follow_prob[i, last_ball - blue_range[0]] * blue_probs_list[last_ball - blue_range[0]]
                blue_prediction_probs[i] = prob_sum

        else:  # full_bayesian
            # 基于全条件贝叶斯概率预测
            red_prediction_probs = np.zeros(red_range[1] - red_range[0] + 1)
            blue_prediction_probs = np.zeros(blue_range[1] - blue_range[0] + 1)

            # 红球全条件贝叶斯概率
            red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
            for i in range(red_range[1] - red_range[0] + 1):
                prob_sum = 0
                for j in range(red_range[1] - red_range[0] + 1):
                    prob_sum += red_follow_prob[i, j] * red_probs_list[j]
                red_prediction_probs[i] = prob_sum

            # 蓝球全条件贝叶斯概率
            blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
            for i in range(blue_range[1] - blue_range[0] + 1):
                prob_sum = 0
                for j in range(blue_range[1] - blue_range[0] + 1):
                    prob_sum += blue_follow_prob[i, j] * blue_probs_list[j]
                blue_prediction_probs[i] = prob_sum

        # 选择概率最高的号码
        red_indices = np.argsort(red_prediction_probs)[-red_count:]
        red_numbers = sorted([i + red_range[0] for i in red_indices])

        blue_indices = np.argsort(blue_prediction_probs)[-blue_count:]
        blue_numbers = sorted([i + blue_range[0] for i in blue_indices])

        return red_numbers + blue_numbers

    def bayesian_prediction_ai(self, train_data):
        """
        基于贝叶斯概率进行预测（AI版本）

        Args:
            train_data: 训练数据

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
            red_count = 6
            blue_count = 1
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)
            red_count = 5
            blue_count = 2

        # 计算频率统计
        red_freq, _ = self.calculate_ball_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        blue_freq, _ = self.calculate_ball_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 计算趋势分析（最近100期）
        recent_periods = min(100, len(train_data))
        recent_data = train_data.tail(recent_periods)

        red_recent_freq = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        for col in red_columns:
            for value in recent_data.iloc[:, col]:
                if red_range[0] <= value <= red_range[1]:
                    red_recent_freq[value] += 1

        blue_recent_freq = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}
        for col in blue_columns:
            for value in recent_data.iloc[:, col]:
                if blue_range[0] <= value <= blue_range[1]:
                    blue_recent_freq[value] += 1

        # 计算红球预测概率
        red_prediction_probs = {}
        total_red_freq = sum(red_freq.values())
        total_red_recent = sum(red_recent_freq.values())

        for ball in range(red_range[0], red_range[1] + 1):
            # 基础概率（历史频率）
            base_prob = red_freq[ball] / total_red_freq if total_red_freq > 0 else 1 / (red_range[1] - red_range[0] + 1)

            # 趋势权重（最近期数的表现）
            trend_weight = red_recent_freq[ball] / total_red_recent if total_red_recent > 0 else 0

            # 综合概率（基础概率 * 0.8 + 趋势权重 * 0.2）
            red_prediction_probs[ball] = base_prob * 0.6 + trend_weight * 0.4

        # 计算蓝球预测概率
        blue_prediction_probs = {}
        total_blue_freq = sum(blue_freq.values())
        total_blue_recent = sum(blue_recent_freq.values())

        for ball in range(blue_range[0], blue_range[1] + 1):
            # 基础概率（历史频率）
            base_prob = blue_freq[ball] / total_blue_freq if total_blue_freq > 0 else 1 / (blue_range[1] - blue_range[0] + 1)

            # 趋势权重（最近期数的表现）
            trend_weight = blue_recent_freq[ball] / total_blue_recent if total_blue_recent > 0 else 0

            # 综合概率（基础概率 * 0.8 + 趋势权重 * 0.2）
            blue_prediction_probs[ball] = base_prob * 0.6 + trend_weight * 0.4

        # 选择概率最高的号码
        red_sorted = sorted(red_prediction_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_prediction_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        return sorted(predicted_red) + sorted(predicted_blue)

    def markov_chain_prediction(self, train_data):
        """
        基于马尔可夫链进行预测

        Args:
            train_data: 训练数据

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
            red_count = 6
            blue_count = 1
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)
            red_count = 5
            blue_count = 2

        # 计算频率统计
        red_freq, _ = self.calculate_ball_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        blue_freq, _ = self.calculate_ball_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 计算趋势分析（最近100期）
        recent_periods = min(100, len(train_data))
        recent_data = train_data.tail(recent_periods)

        red_recent_freq = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        for col in red_columns:
            for value in recent_data.iloc[:, col]:
                if red_range[0] <= value <= red_range[1]:
                    red_recent_freq[value] += 1

        blue_recent_freq = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}
        for col in blue_columns:
            for value in recent_data.iloc[:, col]:
                if blue_range[0] <= value <= blue_range[1]:
                    blue_recent_freq[value] += 1

        # 计算重号分析
        repeat_stats = self.calculate_repeat_analysis()

        # 获取最新一期号码
        if self.lottery_type == "SSQ":
            latest_red = set(train_data.iloc[-1, 1:7].tolist())
            latest_blue = set(train_data.iloc[-1, 7:8].tolist())
        else:
            latest_red = set(train_data.iloc[-1, 1:6].tolist())
            latest_blue = set(train_data.iloc[-1, 6:8].tolist())

        # 计算红球预测概率
        red_prediction_probs = {}
        for ball in range(red_range[0], red_range[1] + 1):
            # 基础频率权重
            base_weight = red_freq[ball] / sum(red_freq.values()) if sum(red_freq.values()) > 0 else 0

            # 趋势权重
            trend_weight = red_recent_freq[ball] / sum(red_recent_freq.values()) if sum(red_recent_freq.values()) > 0 else 0

            # 重号权重（如果上期出现过，降低概率；如果是常见重号，提高概率）
            repeat_weight = 0
            if ball in latest_red:
                # 上期出现过，根据重号统计调整
                repeat_rate = repeat_stats['red_repeat_numbers'][ball] / len(train_data) if len(train_data) > 0 else 0
                repeat_weight = -0.3 + repeat_rate * 0.5  # 基础降权，但常见重号会有补偿
            else:
                # 上期未出现，正常权重
                repeat_weight = 0.1

            # 综合概率
            red_prediction_probs[ball] = base_weight * 0.9 + trend_weight * 0.1 + repeat_weight * 0

        # 计算蓝球预测概率
        blue_prediction_probs = {}
        for ball in range(blue_range[0], blue_range[1] + 1):
            # 基础频率权重
            base_weight = blue_freq[ball] / sum(blue_freq.values()) if sum(blue_freq.values()) > 0 else 0

            # 趋势权重
            trend_weight = blue_recent_freq[ball] / sum(blue_recent_freq.values()) if sum(blue_recent_freq.values()) > 0 else 0

            # 重号权重
            repeat_weight = 0
            if ball in latest_blue:
                repeat_rate = repeat_stats['blue_repeat_numbers'][ball] / len(train_data) if len(train_data) > 0 else 0
                repeat_weight = -0.3 + repeat_rate * 0.5
            else:
                repeat_weight = 0.1

            # 综合概率
            blue_prediction_probs[ball] = base_weight * 0.9 + trend_weight * 0.1 + repeat_weight * 0

        # 选择概率最高的号码
        red_sorted = sorted(red_prediction_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_prediction_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        return sorted(predicted_red) + sorted(predicted_blue)

    def ensemble_prediction(self, train_data):
        """
        集成预测方法，综合多种预测结果

        Args:
            train_data: 训练数据

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if self.lottery_type == "SSQ":
            red_count = 6
            blue_count = 1
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_count = 5
            blue_count = 2
            red_range = (1, 35)
            blue_range = (1, 12)

        # 获取贝叶斯预测结果
        bayesian_numbers = self.bayesian_prediction_ai(train_data)

        # 获取马尔可夫预测结果
        markov_numbers = self.markov_chain_prediction(train_data)

        # 分离红球和蓝球
        if self.lottery_type == "SSQ":
            bayesian_red = bayesian_numbers[:6]
            bayesian_blue = bayesian_numbers[6:7]
            markov_red = markov_numbers[:6]
            markov_blue = markov_numbers[6:7]
        else:
            bayesian_red = bayesian_numbers[:5]
            bayesian_blue = bayesian_numbers[5:7]
            markov_red = markov_numbers[:5]
            markov_blue = markov_numbers[5:7]

        # 计算集成概率
        red_probs = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        blue_probs = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}

        # 红球集成：给预测中的号码加权
        for ball in bayesian_red:
            red_probs[ball] += 0.5  # 贝叶斯默认权重50%
        for ball in markov_red:
            red_probs[ball] += 0.5  # 马尔可夫默认权重50%

        # 蓝球集成：给预测中的号码加权
        for ball in bayesian_blue:
            blue_probs[ball] += 0.5  # 贝叶斯默认权重50%
        for ball in markov_blue:
            blue_probs[ball] += 0.5  # 马尔可夫默认权重50%

        # 选择概率最高的号码
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        return sorted(predicted_red) + sorted(predicted_blue)

    def two_numbers_prediction(self):
        """
        两注号码预测：基于多条件贝叶斯和全条件贝叶斯方法
        """
        print("\n" + "=" * 60)
        print("两注号码预测")
        print("=" * 60)

        # 获取最新一期号码
        latest_period = self.data.iloc[-1, 0]
        if self.lottery_type == "SSQ":
            latest_red = self.data.iloc[-1, 1:7].tolist()
            latest_blue = self.data.iloc[-1, 7:8].tolist()
        else:
            latest_red = self.data.iloc[-1, 1:6].tolist()
            latest_blue = self.data.iloc[-1, 6:8].tolist()

        print(f"上一期{self.lottery_type}第{latest_period}期的号码是：")
        print(f"红球：{latest_red}")
        print(f"蓝球：{latest_blue}")

        # 使用多条件贝叶斯预测
        multi_prediction = self.predict_numbers(self.data, "multi_bayesian")
        if self.lottery_type == "SSQ":
            multi_red = [int(x) for x in multi_prediction[:6]]
            multi_blue = [int(x) for x in multi_prediction[6:7]]
        else:
            multi_red = [int(x) for x in multi_prediction[:5]]
            multi_blue = [int(x) for x in multi_prediction[5:7]]

        print(f"\n基于'多条件贝叶斯概率预测'方法预测的号码是：")
        print(f"红球：{multi_red}")
        print(f"蓝球：{multi_blue}")

        # 使用全条件贝叶斯预测
        full_prediction = self.predict_numbers(self.data, "full_bayesian")
        if self.lottery_type == "SSQ":
            full_red = [int(x) for x in full_prediction[:6]]
            full_blue = [int(x) for x in full_prediction[6:7]]
        else:
            full_red = [int(x) for x in full_prediction[:5]]
            full_blue = [int(x) for x in full_prediction[5:7]]

        print(f"\n基于'全条件贝叶斯概率预测'方法预测的号码是：")
        print(f"红球：{full_red}")
        print(f"蓝球：{full_blue}")

        return True

    def multi_numbers_prediction(self):
        """
        多注号码预测：基于多条件贝叶斯方法，返回更多号码选择
        """
        print("\n" + "=" * 60)
        print("多注号码预测")
        print("=" * 60)

        # 获取最新一期号码
        latest_period = self.data.iloc[-1, 0]
        if self.lottery_type == "SSQ":
            latest_red = self.data.iloc[-1, 1:7].tolist()
            latest_blue = self.data.iloc[-1, 7:8].tolist()
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
            red_count = 6  # SSQ多注预测：红球6个（前5个最大概率+1个最小概率）
            blue_count = 2  # SSQ多注预测：蓝球2个（1个最大概率+1个排序第14）
        else:
            latest_red = self.data.iloc[-1, 1:6].tolist()
            latest_blue = self.data.iloc[-1, 6:8].tolist()
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)
            red_count = 5  # DLT多注预测：红球5个（前5个最大概率）
            blue_count = 2  # DLT多注预测：蓝球2个（前2个最大概率）

        print(f"上一期{self.lottery_type}第{latest_period}期的号码是：")
        print(f"红球：{latest_red}")
        print(f"蓝球：{latest_blue}")

        # 计算多条件贝叶斯概率
        red_prediction_probs, blue_prediction_probs = self.calculate_multi_bayesian_probs()

        # 选择号码（根据彩票类型采用不同策略）
        red_sorted = sorted(red_prediction_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_prediction_probs.items(), key=lambda x: x[1], reverse=True)

        if self.lottery_type == "SSQ":
            # SSQ: 红球前5个概率最大 + 1个概率最小，蓝球概率最大1个 + 排序第14个
            red_top5 = [int(ball) for ball, _ in red_sorted[:5]]
            red_min1 = [int(ball) for ball, _ in red_sorted[-1:]]
            predicted_red = red_top5 + red_min1

            blue_top1 = [int(ball) for ball, _ in blue_sorted[:1]]
            blue_14th = [int(ball) for ball, _ in blue_sorted[13:14]] if len(blue_sorted) >= 14 else [int(ball) for ball, _ in blue_sorted[-1:]]
            predicted_blue = blue_top1 + blue_14th
        else:
            # DLT: 红球前5个概率最大，蓝球前2个概率最大
            predicted_red = [int(ball) for ball, _ in red_sorted[:red_count]]
            predicted_blue = [int(ball) for ball, _ in blue_sorted[:blue_count]]

        print(f"\n基于'多注预测'方法预测的号码是：")
        formatted_multi_pred = self.format_multi_prediction_numbers(predicted_red, predicted_blue)
        print(f"{formatted_multi_pred}")

        # 打印预测策略说明
        if self.lottery_type == "SSQ":
            print(f"\n预测策略说明：")
            print(f"红球：概率最大的前5个号码 + 概率最小的1个号码")
            print(f"蓝球：概率最大的1个号码 + 排序第14的1个号码")
        else:
            print(f"\n预测策略说明：")
            print(f"红球：概率最大的前5个号码")
            print(f"蓝球：概率最大的前2个号码")

        # 打印概率值（显示选中的号码）
        print(f"\n红球选中号码及其概率：")
        for ball in predicted_red:
            prob = red_prediction_probs[ball]
            rank = next(i for i, (b, _) in enumerate(red_sorted) if b == ball) + 1
            print(f"  号码 {ball:2d}: 概率 {prob*100:.4f}% (排序第{rank})")

        print(f"\n蓝球选中号码及其概率：")
        for ball in predicted_blue:
            prob = blue_prediction_probs[ball]
            rank = next(i for i, (b, _) in enumerate(blue_sorted) if b == ball) + 1
            print(f"  号码 {ball:2d}: 概率 {prob*100:.4f}% (排序第{rank})")

        return True

    def calculate_multi_bayesian_probs(self):
        """
        计算多条件贝叶斯概率
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)

        # 统计红球和蓝球的出现次数和概率
        _, red_probs = self.calculate_ball_statistics(
            self.data, red_columns, red_range[0], red_range[1])
        _, blue_probs = self.calculate_ball_statistics(
            self.data, blue_columns, blue_range[0], blue_range[1])

        # 统计跟随性
        _, red_follow_prob = self.calculate_follow_statistics(
            self.data, red_columns, red_range[0], red_range[1])
        _, blue_follow_prob = self.calculate_follow_statistics(
            self.data, blue_columns, blue_range[0], blue_range[1])

        # 获取最新一期号码
        last_numbers = self.data.iloc[-1, 1:].values

        # 红球多条件贝叶斯概率
        red_prediction_probs = {}
        red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
        for i in range(red_range[1] - red_range[0] + 1):
            prob_sum = 0
            for j, col in enumerate(red_columns):
                last_ball = last_numbers[col - 1]  # 获取最新一期对应位置的球号
                if red_range[0] <= last_ball <= red_range[1]:
                    prob_sum += red_follow_prob[i, last_ball - red_range[0]] * red_probs_list[last_ball - red_range[0]]
            red_prediction_probs[i + red_range[0]] = prob_sum

        # 蓝球多条件贝叶斯概率
        blue_prediction_probs = {}
        blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
        for i in range(blue_range[1] - blue_range[0] + 1):
            prob_sum = 0
            for j, col in enumerate(blue_columns):
                last_ball = last_numbers[col - 1]  # 获取最新一期对应位置的球号
                if blue_range[0] <= last_ball <= blue_range[1]:
                    prob_sum += blue_follow_prob[i, last_ball - blue_range[0]] * blue_probs_list[last_ball - blue_range[0]]
            blue_prediction_probs[i + blue_range[0]] = prob_sum

        return red_prediction_probs, blue_prediction_probs

    def format_lottery_numbers(self, numbers):
        """
        格式化彩票号码显示，在红球和蓝球之间添加"+"分隔符

        Args:
            numbers: 号码列表

        Returns:
            格式化后的字符串
        """
        if self.lottery_type == "SSQ":
            # 双色球：前6个红球 + 1个蓝球
            red_balls = numbers[:6]
            blue_balls = numbers[6:7]
        else:
            # 大乐透：前5个红球 + 2个蓝球
            red_balls = numbers[:5]
            blue_balls = numbers[5:7]

        red_str = str(red_balls)
        blue_str = str(blue_balls)
        return f"{red_str} + {blue_str}"

    def format_multi_prediction_numbers(self, red_balls, blue_balls):
        """
        格式化多注预测号码显示，红球和蓝球分别显示

        Args:
            red_balls: 红球号码列表
            blue_balls: 蓝球号码列表

        Returns:
            格式化后的字符串
        """
        red_str = str(red_balls)
        blue_str = str(blue_balls)
        return f"红球{red_str} + 蓝球{blue_str}"

    def single_analysis(self):
        """
        单次分析：分析指定期号
        """
        print("\n" + "=" * 60)
        print("单次分析")
        print("=" * 60)

        # 循环询问用户要分析的期号，直到输入正确
        while True:
            period_input = self.get_user_input("请输入要分析的期号（例如：23001）: ")

            if not period_input:
                print("输入错误，请重新输入期号")
                continue

            try:
                target_period = int(period_input)
            except ValueError:
                print("输入错误，请重新输入期号（请输入数字）")
                continue

            # 查找目标期号在数据中的位置
            target_row = None
            for i, row in self.data.iterrows():
                if row.iloc[0] == target_period:
                    target_row = i
                    break

            if target_row is None:
                print(f"输入错误，请重新输入期号（未找到期号 {target_period}）")
                continue

            # 输入正确，跳出循环
            break

        # 检查是否有足够的后续数据（至少6期）
        if target_row + 6 >= len(self.data):
            print(f"期号 {target_period} 后续数据不足6期，无法进行分析")
            return False

        print(f"正在分析第 {target_period} 期...")

        # 使用目标期号之前的数据作为训练数据
        train_data = self.data.iloc[:target_row].copy()

        if len(train_data) < 10:
            print("训练数据不足，无法进行分析")
            return False

        # 获取目标期号及后续5期的实际号码（共6期作为标准）
        standard_periods = []
        for i in range(6):
            period_row = target_row + i
            period_number = self.data.iloc[period_row, 0]
            if self.lottery_type == "SSQ":
                actual_numbers = self.data.iloc[period_row, 1:8].tolist()
            else:
                actual_numbers = self.data.iloc[period_row, 1:8].tolist()
            standard_periods.append({
                'period': period_number,
                'numbers': actual_numbers
            })

        # 进行两注号码预测
        multi_prediction = self.predict_numbers(train_data, "multi_bayesian")
        full_prediction = self.predict_numbers(train_data, "full_bayesian")

        # 进行多注号码预测
        red_probs, blue_probs = self.calculate_multi_bayesian_probs_for_data(train_data)

        if self.lottery_type == "SSQ":
            multi_red = [int(x) for x in multi_prediction[:6]]
            multi_blue = [int(x) for x in multi_prediction[6:7]]
            full_red = [int(x) for x in full_prediction[:6]]
            full_blue = [int(x) for x in full_prediction[6:7]]

            red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
            blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)
            # SSQ: 红球前5个概率最大 + 1个概率最小，蓝球概率最大1个 + 排序第14个
            red_top5 = [int(ball) for ball, _ in red_sorted[:5]]
            red_min1 = [int(ball) for ball, _ in red_sorted[-1:]]
            multi_pred_red = red_top5 + red_min1  # 共6个红球

            blue_top1 = [int(ball) for ball, _ in blue_sorted[:1]]
            blue_14th = [int(ball) for ball, _ in blue_sorted[13:14]] if len(blue_sorted) >= 14 else [int(ball) for ball, _ in blue_sorted[-1:]]
            multi_pred_blue = blue_top1 + blue_14th  # 共2个蓝球
        else:
            multi_red = [int(x) for x in multi_prediction[:5]]
            multi_blue = [int(x) for x in multi_prediction[5:7]]
            full_red = [int(x) for x in full_prediction[:5]]
            full_blue = [int(x) for x in full_prediction[5:7]]

            red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
            blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)
            # DLT: 红球前5个概率最大，蓝球前2个概率最大
            multi_pred_red = [int(ball) for ball, _ in red_sorted[:5]]  # DLT: 5个红球
            multi_pred_blue = [int(ball) for ball, _ in blue_sorted[:2]]  # DLT: 2个蓝球

        # 打印预测结果
        print(f"\n两注号码预测结果：")
        print(f"多条件贝叶斯预测：红球 {multi_red}，蓝球 {multi_blue}")
        print(f"全条件贝叶斯预测：红球 {full_red}，蓝球 {full_blue}")

        print(f"\n多注号码预测结果：")
        formatted_multi_pred = self.format_multi_prediction_numbers(multi_pred_red, multi_pred_blue)
        print(f"{formatted_multi_pred}")

        # 打印预测策略说明
        if self.lottery_type == "SSQ":
            print(f"\n预测策略说明：")
            print(f"红球：概率最大的前5个号码 + 概率最小的1个号码")
            print(f"蓝球：概率最大的1个号码 + 排序第14的1个号码")
        else:
            print(f"\n预测策略说明：")
            print(f"红球：概率最大的前5个号码")
            print(f"蓝球：概率最大的前2个号码")

        # 打印选中号码的概率值
        print(f"\n红球选中号码及其概率：")
        for ball in multi_pred_red:
            prob = red_probs[ball]
            rank = next(i for i, (b, _) in enumerate(red_sorted) if b == ball) + 1
            print(f"  号码 {ball:2d}: 概率 {prob*100:.4f}% (排序第{rank})")

        print(f"\n蓝球选中号码及其概率：")
        for ball in multi_pred_blue:
            prob = blue_probs[ball]
            rank = next(i for i, (b, _) in enumerate(blue_sorted) if b == ball) + 1
            print(f"  号码 {ball:2d}: 概率 {prob*100:.4f}% (排序第{rank})")

        # 与后续6期进行比对 - 多条件贝叶斯
        print(f"\n多条件贝叶斯与后续6期号码比对结果：")
        multi_best_match = None
        multi_max_hits = -1

        for period_info in standard_periods:
            period_num = period_info['period']
            actual_numbers = period_info['numbers']

            # 计算多条件贝叶斯预测的命中数
            multi_hit_info = self.check_hit_rate(multi_prediction, actual_numbers)
            multi_hits = multi_hit_info['total_hits']

            # 格式化实际号码显示
            formatted_numbers = self.format_lottery_numbers(actual_numbers)
            print(f"第 {period_num} 期：实际号码 {formatted_numbers}，多条件贝叶斯命中 {multi_hits} 个")

            # 更新最佳匹配
            if multi_hits > multi_max_hits or (multi_hits == multi_max_hits and (multi_best_match is None or period_num < multi_best_match['period'])):
                multi_max_hits = multi_hits
                multi_best_match = {
                    'period': period_num,
                    'numbers': actual_numbers,
                    'hits': multi_hits,
                    'hit_info': multi_hit_info
                }

        print(f"\n多条件贝叶斯最佳匹配结果：")
        print(f"期号：{multi_best_match['period']}")
        formatted_multi_best_numbers = self.format_lottery_numbers(multi_best_match['numbers'])
        print(f"实际号码：{formatted_multi_best_numbers}")
        print(f"命中数量：{multi_best_match['hits']} 个")
        print(f"命中详情：红球 {multi_best_match['hit_info']['red_hits']} 个，蓝球 {multi_best_match['hit_info']['blue_hits']} 个")

        # 与后续6期进行比对 - 全条件贝叶斯
        print(f"\n全条件贝叶斯与后续6期号码比对结果：")
        full_best_match = None
        full_max_hits = -1

        for period_info in standard_periods:
            period_num = period_info['period']
            actual_numbers = period_info['numbers']

            # 计算全条件贝叶斯预测的命中数
            full_hit_info = self.check_hit_rate(full_prediction, actual_numbers)
            full_hits = full_hit_info['total_hits']

            # 格式化实际号码显示
            formatted_numbers = self.format_lottery_numbers(actual_numbers)
            print(f"第 {period_num} 期：实际号码 {formatted_numbers}，全条件贝叶斯命中 {full_hits} 个")

            # 更新最佳匹配
            if full_hits > full_max_hits or (full_hits == full_max_hits and (full_best_match is None or period_num < full_best_match['period'])):
                full_max_hits = full_hits
                full_best_match = {
                    'period': period_num,
                    'numbers': actual_numbers,
                    'hits': full_hits,
                    'hit_info': full_hit_info
                }

        print(f"\n全条件贝叶斯最佳匹配结果：")
        print(f"期号：{full_best_match['period']}")
        formatted_full_best_numbers = self.format_lottery_numbers(full_best_match['numbers'])
        print(f"实际号码：{formatted_full_best_numbers}")
        print(f"命中数量：{full_best_match['hits']} 个")
        print(f"命中详情：红球 {full_best_match['hit_info']['red_hits']} 个，蓝球 {full_best_match['hit_info']['blue_hits']} 个")

        # 与后续6期进行比对 - 多注预测
        print(f"\n多注预测与后续6期号码比对结果：")
        multi_pred_best_match = None
        multi_pred_max_hits = -1

        # 用于命中率计算的完整号码列表（按标准格式）
        if self.lottery_type == "SSQ":
            # 对于命中率计算，取6个红球和1个蓝球（标准格式）
            multi_pred_numbers = multi_pred_red[:6] + multi_pred_blue[:1]
        else:
            # 对于命中率计算，取5个红球和2个蓝球（标准格式）
            multi_pred_numbers = multi_pred_red[:5] + multi_pred_blue[:2]

        for period_info in standard_periods:
            period_num = period_info['period']
            actual_numbers = period_info['numbers']

            # 计算多注预测的命中数
            multi_pred_hit_info = self.check_hit_rate(multi_pred_numbers, actual_numbers)
            multi_pred_hits = multi_pred_hit_info['total_hits']

            # 格式化实际号码显示
            formatted_numbers = self.format_lottery_numbers(actual_numbers)
            print(f"第 {period_num} 期：实际号码 {formatted_numbers}，多注预测命中 {multi_pred_hits} 个")

            # 更新最佳匹配
            if multi_pred_hits > multi_pred_max_hits or (multi_pred_hits == multi_pred_max_hits and (multi_pred_best_match is None or period_num < multi_pred_best_match['period'])):
                multi_pred_max_hits = multi_pred_hits
                multi_pred_best_match = {
                    'period': period_num,
                    'numbers': actual_numbers,
                    'hits': multi_pred_hits,
                    'hit_info': multi_pred_hit_info
                }

        print(f"\n多注预测最佳匹配结果：")
        print(f"期号：{multi_pred_best_match['period']}")
        formatted_multi_pred_best_numbers = self.format_lottery_numbers(multi_pred_best_match['numbers'])
        print(f"实际号码：{formatted_multi_pred_best_numbers}")
        print(f"命中数量：{multi_pred_best_match['hits']} 个")
        print(f"命中详情：红球 {multi_pred_best_match['hit_info']['red_hits']} 个，蓝球 {multi_pred_best_match['hit_info']['blue_hits']} 个")

        return True

    def calculate_multi_bayesian_probs_for_data(self, data):
        """
        为指定数据计算多条件贝叶斯概率
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)

        # 统计红球和蓝球的出现次数和概率
        _, red_probs = self.calculate_ball_statistics(
            data, red_columns, red_range[0], red_range[1])
        _, blue_probs = self.calculate_ball_statistics(
            data, blue_columns, blue_range[0], blue_range[1])

        # 统计跟随性
        _, red_follow_prob = self.calculate_follow_statistics(
            data, red_columns, red_range[0], red_range[1])
        _, blue_follow_prob = self.calculate_follow_statistics(
            data, blue_columns, blue_range[0], blue_range[1])

        # 获取最新一期号码
        last_numbers = data.iloc[-1, 1:].values

        # 红球多条件贝叶斯概率
        red_prediction_probs = {}
        red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
        for i in range(red_range[1] - red_range[0] + 1):
            prob_sum = 0
            for j, col in enumerate(red_columns):
                last_ball = last_numbers[col - 1]
                if red_range[0] <= last_ball <= red_range[1]:
                    prob_sum += red_follow_prob[i, last_ball - red_range[0]] * red_probs_list[last_ball - red_range[0]]
            red_prediction_probs[i + red_range[0]] = prob_sum

        # 蓝球多条件贝叶斯概率
        blue_prediction_probs = {}
        blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
        for i in range(blue_range[1] - blue_range[0] + 1):
            prob_sum = 0
            for j, col in enumerate(blue_columns):
                last_ball = last_numbers[col - 1]
                if blue_range[0] <= last_ball <= blue_range[1]:
                    prob_sum += blue_follow_prob[i, last_ball - blue_range[0]] * blue_probs_list[last_ball - blue_range[0]]
            blue_prediction_probs[i + blue_range[0]] = prob_sum

        return red_prediction_probs, blue_prediction_probs

    def continuous_analysis(self):
        """
        连续分析：从指定期号开始连续分析最多500期
        """
        print("\n" + "=" * 60)
        print("连续分析")
        print("=" * 60)

        # 循环询问用户开始期号，直到输入正确
        while True:
            period_input = self.get_user_input("请输入开始分析的期号（例如：23001）: ")

            if not period_input:
                print("输入错误，请重新输入期号")
                continue

            try:
                start_period = int(period_input)
            except ValueError:
                print("输入错误，请重新输入期号（请输入数字）")
                continue

            # 查找开始期号在数据中的位置
            start_row = None
            for i, row in self.data.iterrows():
                if row.iloc[0] == start_period:
                    start_row = i
                    break

            if start_row is None:
                print(f"输入错误，请重新输入期号（未找到期号 {start_period}）")
                continue

            # 输入正确，跳出循环
            break

        print(f"开始连续分析，从第 {start_period} 期开始...")

        # 存储分析结果
        analysis_results = []

        # 最多分析500期
        for i in range(500):
            current_row = start_row + i
            current_period = self.data.iloc[current_row, 0]

            # 检查是否超出数据范围（需要确保后面还有6期数据可以比对）
            if current_row + 6 >= len(self.data):
                print(f"第 {current_period} 期后续数据不足6期，停止分析")
                break

            # 使用当前期号之前的数据作为训练数据
            train_data = self.data.iloc[:current_row].copy()

            if len(train_data) < 10:
                print(f"第 {current_period} 期训练数据不足，跳过")
                continue

            # 获取当前期号及后续5期的实际号码（共6期作为标准）
            standard_periods = []
            for j in range(6):
                period_row = current_row + j
                period_number = self.data.iloc[period_row, 0]
                if self.lottery_type == "SSQ":
                    actual_numbers = self.data.iloc[period_row, 1:8].tolist()
                else:
                    actual_numbers = self.data.iloc[period_row, 1:8].tolist()
                standard_periods.append({
                    'period': period_number,
                    'numbers': actual_numbers
                })

            # 进行预测
            multi_prediction = self.predict_numbers(train_data, "multi_bayesian")
            full_prediction = self.predict_numbers(train_data, "full_bayesian")

            # 进行多注号码预测
            red_probs, blue_probs = self.calculate_multi_bayesian_probs_for_data(train_data)

            if self.lottery_type == "SSQ":
                red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
                blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)
                # SSQ: 红球前5个概率最大 + 1个概率最小，蓝球概率最大1个 + 排序第14个
                red_top5 = [int(ball) for ball, _ in red_sorted[:5]]
                red_min1 = [int(ball) for ball, _ in red_sorted[-1:]]
                multi_pred_red = red_top5 + red_min1  # 共6个红球

                blue_top1 = [int(ball) for ball, _ in blue_sorted[:1]]
                blue_14th = [int(ball) for ball, _ in blue_sorted[13:14]] if len(blue_sorted) >= 14 else [int(ball) for ball, _ in blue_sorted[-1:]]
                multi_pred_blue = blue_top1 + blue_14th  # 共2个蓝球
            else:
                red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
                blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)
                # DLT: 红球前5个概率最大，蓝球前2个概率最大
                multi_pred_red = [int(ball) for ball, _ in red_sorted[:5]]  # DLT: 5个红球
                multi_pred_blue = [int(ball) for ball, _ in blue_sorted[:2]]  # DLT: 2个蓝球

            # 用于命中率计算的完整号码列表（按标准格式）
            if self.lottery_type == "SSQ":
                # 对于命中率计算，取6个红球和1个蓝球（标准格式）
                multi_pred_numbers = multi_pred_red[:6] + multi_pred_blue[:1]
            else:
                # 对于命中率计算，取5个红球和2个蓝球（标准格式）
                multi_pred_numbers = multi_pred_red[:5] + multi_pred_blue[:2]

            # 与标准6期进行比对，找出三种预测方法中的最佳匹配
            best_match = None
            max_hits = -1
            best_method = ""

            for period_info in standard_periods:
                period_num = period_info['period']
                actual_numbers = period_info['numbers']

                # 计算三种预测方法的命中数
                multi_hit_info = self.check_hit_rate(multi_prediction, actual_numbers)
                multi_hits = multi_hit_info['total_hits']

                full_hit_info = self.check_hit_rate(full_prediction, actual_numbers)
                full_hits = full_hit_info['total_hits']

                multi_pred_hit_info = self.check_hit_rate(multi_pred_numbers, actual_numbers)
                multi_pred_hits = multi_pred_hit_info['total_hits']

                # 找出当前期号中命中数最多的预测方法
                current_max_hits = max(multi_hits, full_hits, multi_pred_hits)
                current_best_method = ""

                if current_max_hits == multi_hits:
                    current_best_method = "两注多条件贝叶斯预测"
                elif current_max_hits == full_hits:
                    current_best_method = "两注全条件贝叶斯预测"
                else:
                    current_best_method = "多注预测"

                # 如果有多个方法命中数相同，按优先级选择
                if current_max_hits == multi_hits and current_max_hits == full_hits and current_max_hits == multi_pred_hits:
                    current_best_method = "两注多条件贝叶斯预测"
                elif current_max_hits == multi_hits and current_max_hits == full_hits:
                    current_best_method = "两注多条件贝叶斯预测"
                elif current_max_hits == multi_hits and current_max_hits == multi_pred_hits:
                    current_best_method = "两注多条件贝叶斯预测"
                elif current_max_hits == full_hits and current_max_hits == multi_pred_hits:
                    current_best_method = "两注全条件贝叶斯预测"

                # 更新全局最佳匹配（命中数最多，如果相同则选期号最小的）
                if current_max_hits > max_hits or (current_max_hits == max_hits and (best_match is None or period_num < best_match['period'])):
                    max_hits = current_max_hits
                    best_match = {
                        'period': period_num,
                        'numbers': actual_numbers,
                        'hits': current_max_hits
                    }
                    best_method = current_best_method

            # 格式化预测号码显示
            multi_prediction_formatted = self.format_lottery_numbers([int(x) for x in multi_prediction])
            full_prediction_formatted = self.format_lottery_numbers([int(x) for x in full_prediction])
            # 多注预测号码使用特殊格式，显示完整的红球和蓝球数量
            multi_pred_numbers_formatted = self.format_multi_prediction_numbers(multi_pred_red, multi_pred_blue)
            best_match_numbers_formatted = self.format_lottery_numbers(best_match['numbers'])

            # 记录分析结果
            result = {
                'sequence': i + 1,
                'analysis_period': current_period,
                'multi_bayesian_prediction': multi_prediction_formatted,
                'full_bayesian_prediction': full_prediction_formatted,
                'multi_numbers_prediction': multi_pred_numbers_formatted,
                'best_match_period': best_match['period'],
                'best_match_numbers': best_match_numbers_formatted,
                'best_match_hits': best_match['hits'],
                'best_match_method': best_method
            }

            analysis_results.append(result)

            # 显示进度
            if (i + 1) % 50 == 0:
                print(f"已完成 {i + 1} 期分析...")

        print(f"\n连续分析完成！共分析了 {len(analysis_results)} 期")

        # 保存结果到Excel
        return self.save_continuous_analysis_results(analysis_results)

    def save_continuous_analysis_results(self, results):
        """
        保存连续分析结果到Excel文件
        """
        if not results:
            print("没有分析结果可保存")
            return False

        try:
            # 创建结果DataFrame
            results_data = []
            for result in results:
                row = {
                    '连续分析序号': result['sequence'],
                    '分析期号': result['analysis_period'],
                    '两注多条件贝叶斯预测': result['multi_bayesian_prediction'],
                    '两注全条件贝叶斯预测': result['full_bayesian_prediction'],
                    '多注预测号码': result['multi_numbers_prediction'],
                    '最佳匹配期号': result['best_match_period'],
                    '最佳匹配号码': result['best_match_numbers'],
                    '最佳匹配命中数': result['best_match_hits'],
                    '最佳匹配预测方法': result['best_match_method']
                }
                results_data.append(row)

            results_df = pd.DataFrame(results_data)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"continuous_analysis_results_{self.lottery_type}_{timestamp}.xlsx"

            # 保存到Excel文件
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                results_df.to_excel(writer, sheet_name='连续分析结果', index=False)

                # 创建汇总统计
                total_analysis = len(results)
                avg_hits = sum(r['best_match_hits'] for r in results) / total_analysis if total_analysis > 0 else 0
                max_hits = max(r['best_match_hits'] for r in results) if results else 0
                min_hits = min(r['best_match_hits'] for r in results) if results else 0

                summary_data = {
                    '统计项目': ['彩票类型', '总分析期数', '平均命中数', '最大命中数', '最小命中数'],
                    '数值': [self.lottery_type, total_analysis, f"{avg_hits:.2f}", max_hits, min_hits]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

            print(f"连续分析结果已保存到文件: {filename}")
            return True

        except Exception as e:
            print(f"保存连续分析结果时出错: {str(e)}")
            return False

    def check_hit_rate(self, predicted, actual):
        """
        检查预测号码与实际号码的命中情况

        Args:
            predicted: 预测号码列表
            actual: 实际号码列表

        Returns:
            hit_info: 命中信息字典
        """
        if self.lottery_type == "SSQ":
            # 双色球：前6个是红球，第7个是蓝球
            pred_red = set(predicted[:6])
            pred_blue = set(predicted[6:7])
            actual_red = set(actual[:6])
            actual_blue = set(actual[6:7])

            red_hits = len(pred_red & actual_red)
            blue_hits = len(pred_blue & actual_blue)

            # SSQ中奖条件：红球至少5个相等，蓝球1个相等
            is_hit = red_hits >= 5 and blue_hits == 1

        else:  # DLT
            # 大乐透：前5个是红球，后2个是蓝球
            pred_red = set(predicted[:5])
            pred_blue = set(predicted[5:7])
            actual_red = set(actual[:5])
            actual_blue = set(actual[5:7])

            red_hits = len(pred_red & actual_red)
            blue_hits = len(pred_blue & actual_blue)

            # DLT中奖条件：红球至少4个相等，蓝球2个相等
            is_hit = red_hits >= 4 and blue_hits == 2

        return {
            'red_hits': red_hits,
            'blue_hits': blue_hits,
            'total_hits': red_hits + blue_hits,
            'is_hit': is_hit
        }

    def step3_calculate_and_compare(self):
        """
        第三步：开始计算和比对
        修改：比对范围为指定行之后的12期数据，选择命中号码数量最多的期号作为比对结果
        若存在命中数量相同的情况，选择与指定行最近的期号
        """
        print("\n" + "=" * 60)
        print("第三步：开始计算和比对")
        print("=" * 60)

        print(f"开始从第 {self.start_row} 行进行计算...")
        print(f"使用方法: {self.method}")
        print(f"将进行最多500次计算和比对...")
        print(f"每次预测将与后续12期数据进行比对，选择命中数量最多的期号...")
        print(f"若命中数量相同，则选择与指定行最近的期号...")

        self.results = []
        hit_count = 0

        for i in range(500):
            current_row = self.start_row + i

            # 检查是否超出数据范围（需要确保后面还有12期数据可以比对）
            if current_row + 12 >= len(self.data):
                print(f"警告: 第 {current_row} 行后续数据不足12期，停止计算")
                break

            # 获取训练数据（从开始到当前行）
            train_data = self.data.iloc[:current_row].copy()

            # 预测下一期号码
            predicted_numbers = self.predict_numbers(train_data, self.method)

            if predicted_numbers is None:
                print(f"第 {i+1} 次计算失败：训练数据不足")
                continue

            # 与后续12期数据进行比对，找出命中号码数量最多的期号
            best_hit_info = None
            best_target_period = None
            best_actual_numbers = None
            max_hit_count = -1
            closest_period_index = None  # 记录最近期号的索引

            # 遍历后续12期数据，找出命中数量最多的期号
            for j in range(1, 13):  # 检查后续12期数据（指定行+1到指定行+12）
                target_row = current_row + j
                actual_numbers = self.data.iloc[target_row, 1:].values.tolist()

                # 检查命中情况
                hit_info = self.check_hit_rate(predicted_numbers, actual_numbers)
                current_hit_count = hit_info['total_hits']

                # 如果当前期号的命中数量更多，或者命中数量相同但期号更近，则更新最佳结果
                if (current_hit_count > max_hit_count or
                    (current_hit_count == max_hit_count and (closest_period_index is None or j < closest_period_index))):
                    max_hit_count = current_hit_count
                    best_hit_info = hit_info
                    best_target_period = self.data.iloc[target_row, 0]
                    best_actual_numbers = actual_numbers
                    closest_period_index = j

            # 如果没有找到任何比对结果（理论上不会发生），使用第一期作为默认
            if best_hit_info is None:
                target_row = current_row + 1
                actual_numbers = self.data.iloc[target_row, 1:].values.tolist()
                hit_info = self.check_hit_rate(predicted_numbers, actual_numbers)
                best_target_period = self.data.iloc[target_row, 0]
                best_actual_numbers = actual_numbers
                best_hit_info = hit_info

            # 记录结果
            result = {
                'iteration': i + 1,
                'base_period': self.data.iloc[current_row - 1, 0],  # 基于的期号
                'target_period': best_target_period,    # 比对的期号
                'method': self.method,
                'predicted_numbers': predicted_numbers,
                'actual_numbers': best_actual_numbers,
                'red_hits': best_hit_info['red_hits'],
                'blue_hits': best_hit_info['blue_hits'],
                'total_hits': best_hit_info['total_hits'],
                'is_hit': best_hit_info['is_hit'],
                'hit_in_12_periods': best_hit_info['is_hit'],  # 标记是否在12期内命中
                'max_hit_count': max_hit_count,  # 记录最大命中数量
                'closest_period_index': closest_period_index  # 记录最近期号的索引
            }

            self.results.append(result)

            # 如果命中，打印结果
            if best_hit_info['is_hit']:
                hit_count += 1
                print(f"命中！第 {i+1} 次计算:")
                print(f"  基于第 {result['base_period']} 期号码")
                print(f"  用 {self.method} 方法预测")
                print(f"  与第 {result['target_period']} 期号码命中")
                print(f"  命中 {best_hit_info['total_hits']} 个号码")
                print(f"  (红球: {best_hit_info['red_hits']}, 蓝球: {best_hit_info['blue_hits']})")
            else:
                # 即使没有达到中奖标准，也显示最佳匹配结果
                print(f"第 {i+1} 次计算:")
                print(f"  基于第 {result['base_period']} 期号码")
                print(f"  用 {self.method} 方法预测")
                print(f"  最佳匹配：第 {result['target_period']} 期，命中 {best_hit_info['total_hits']} 个号码")
                print(f"  (红球: {best_hit_info['red_hits']}, 蓝球: {best_hit_info['blue_hits']})")

            # 显示进度
            if (i + 1) % 50 == 0:
                print(f"已完成 {i + 1}/500 次计算，当前命中次数: {hit_count}")

        print(f"\n第三步完成！")
        print(f"总计算次数: {len(self.results)}")
        print(f"总命中次数: {hit_count}")
        print(f"命中率: {hit_count/len(self.results)*100:.2f}%" if self.results else "0%")

        return True

    def step4_save_results_to_excel(self):
        """
        第四步：将校核结果保存在Excel文件中
        """
        print("\n" + "=" * 60)
        print("第四步：将校核结果保存在Excel文件中")
        print("=" * 60)

        if not self.results:
            print("没有结果数据可保存")
            return False

        try:
            # 创建结果DataFrame
            results_data = []
            for result in self.results:
                row = {
                    '计算次序': result['iteration'],
                    '基于期号': result['base_period'],
                    '比对期号': result['target_period'],
                    '计算方法': result['method'],
                    '预测号码': str(result['predicted_numbers']),
                    '实际号码': str(result['actual_numbers']),
                    '红球命中数': result['red_hits'],
                    '蓝球命中数': result['blue_hits'],
                    '总命中数': result['total_hits'],
                    '是否命中': '是' if result['is_hit'] else '否',
                    '12期内命中': '是' if result.get('hit_in_12_periods', False) else '否',
                    '最大命中数': result.get('max_hit_count', result['total_hits']),
                    '期号距离': result.get('closest_period_index', 1)
                }
                results_data.append(row)

            results_df = pd.DataFrame(results_data)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"lottery_analysis_results_{self.lottery_type}_{timestamp}.xlsx"

            # 保存到Excel文件
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 保存详细结果
                results_df.to_excel(writer, sheet_name='详细结果', index=False)

                # 创建汇总统计
                total_calculations = len(self.results)
                total_hits = sum(1 for r in self.results if r['is_hit'])
                total_hits_in_12 = sum(1 for r in self.results if r.get('hit_in_12_periods', False))
                hit_rate = total_hits / total_calculations * 100 if total_calculations > 0 else 0
                hit_rate_in_12 = total_hits_in_12 / total_calculations * 100 if total_calculations > 0 else 0

                summary_data = {
                    '统计项目': ['彩票类型', '计算方法', '开始行数', '总计算次数', '总命中次数', '命中率(%)', '12期内命中次数', '12期内命中率(%)'],
                    '数值': [self.lottery_type, self.method, self.start_row,
                            total_calculations, total_hits, f"{hit_rate:.2f}", total_hits_in_12, f"{hit_rate_in_12:.2f}"]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

                # 保存命中详情
                hit_results = [r for r in self.results if r['is_hit']]
                if hit_results:
                    hit_data = []
                    for result in hit_results:
                        row = {
                            '计算次序': result['iteration'],
                            '基于期号': result['base_period'],
                            '比对期号': result['target_period'],
                            '预测号码': str(result['predicted_numbers']),
                            '实际号码': str(result['actual_numbers']),
                            '命中详情': f"红球{result['red_hits']}个，蓝球{result['blue_hits']}个",
                            '12期内命中': '是' if result.get('hit_in_12_periods', False) else '否'
                        }
                        hit_data.append(row)

                    hit_df = pd.DataFrame(hit_data)
                    hit_df.to_excel(writer, sheet_name='命中详情', index=False)

            print(f"结果已保存到文件: {filename}")
            print(f"包含工作表: 详细结果、汇总统计" + ("、命中详情" if hit_results else ""))

            return True

        except Exception as e:
            print(f"保存结果时出错: {str(e)}")
            return False

    def run_analysis(self):
        """
        运行完整的分析流程
        """
        print("开始彩票数据分析程序")
        print("=" * 60)

        # 第一步：选择彩票类型
        if not self.step1_select_lottery_type():
            return False

        # 读取数据（根据选择的彩票类型）
        if not self.read_and_sort_data():
            return False

        # 根据彩票类型设置数据
        if self.lottery_type == "DLT":
            self.data = self.dlthistory_allout
        else:
            self.data = self.ssqhistory_allout

        # 第二步：选择操作模式
        if not self.step2_select_operation_mode():
            return False

        # 根据选择的模式执行相应操作
        if self.operation_mode == "prediction":
            if self.prediction_mode == "two":
                return self.two_numbers_prediction()
            elif self.prediction_mode == "multi":
                return self.multi_numbers_prediction()
        elif self.operation_mode == "analysis":
            if self.analysis_mode == "single":
                return self.single_analysis()
            elif self.analysis_mode == "continuous":
                return self.continuous_analysis()

        print("\n" + "=" * 60)
        print("程序执行完成！")
        print("=" * 60)

        return True

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = LotteryAnalyzer()

    # 运行完整分析流程
    analyzer.run_analysis()
