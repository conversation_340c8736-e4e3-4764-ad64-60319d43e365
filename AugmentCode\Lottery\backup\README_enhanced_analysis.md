# 彩票数据分析增强版程序

## 程序概述

基于Lottery_Final.py程序开发的增强版彩票分析程序，提供详细的历史数据分析功能，包括奇偶比、质合比、大小比、和值走势等多维度分析。

## 主要功能

### 1. 彩票类型支持
- **SSQ (双色球)**：红球1-33，蓝球1-16
- **DLT (大乐透)**：红球1-35，蓝球1-12

### 2. 分析模式
- **预测模式**：暂时留空，后续增加
- **分析模式**：
  - 单次分析：分析指定期号
  - 连续分析：从指定期号开始连续分析最多500期

### 3. 历史数据统计分析

#### 奇偶比分析
- 统计红球和蓝球的奇偶比分布
- 显示各种奇偶比组合的出现次数和占比
- 例如：SSQ红球的6:0、5:1、4:2、3:3、2:4、1:5、0:6占比

#### 质合比分析
- 统计红球和蓝球的质数与合数比分布
- 质数：2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31等
- 显示各种质合比组合的出现次数和占比

#### 大小比分析
- **SSQ**：红球大于16、蓝球大于8为大球
- **DLT**：红球大于17、蓝球大于6为大球
- 统计大小球比例分布

#### 和值走势分析
- 按每10个数为一个区间统计和值分布
- 红球和值区间分布
- 蓝球和值分布（单个蓝球时和值为号码本身）

### 4. 单期分析功能

#### a. 历史出现概率分布
- 统计所有红球和蓝球的历史出现概率
- 按概率从大到小排序
- 显示分析期号码在概率排序中的位置
- 输出红球和蓝球的最后排序位置

#### b. 跟随性概率分布
- 基于上一期号码计算多条件贝叶斯预测概率
- 按概率从大到小排序
- 显示分析期号码在跟随性概率排序中的位置
- 输出红球和蓝球的最后排序位置

### 5. 连续分析功能
- 从指定期号开始连续分析
- 最多分析500期
- 自动保存分析结果到Excel文件
- 包含详细的分析数据和汇总统计

## 程序结构

### 模块化设计
- `LotteryAnalysisModule`：分析模块基类
- `OddEvenAnalysisModule`：奇偶比分析模块
- `PrimeCompositeAnalysisModule`：质合比分析模块
- `LargeSmalAnalysisModule`：大小比分析模块
- `SumTrendAnalysisModule`：和值走势分析模块
- `HistoricalProbabilityModule`：历史概率分布分析模块
- `FollowingProbabilityModule`：跟随性概率分析模块
- `LotteryAnalyzerEnhanced`：主分析器类

### 核心功能沿用
- 基于Lottery_Final.py的核心算法
- Excel数据读取和处理逻辑
- 多条件贝叶斯概率计算

## 使用方法

### 1. 运行程序
```bash
python lottery_analysis_enhanced.py
```

### 2. 选择彩票类型
- 输入1选择SSQ（双色球）
- 输入2选择DLT（大乐透）

### 3. 选择操作模式
- 输入1选择预测模式（暂未实现）
- 输入2选择分析模式

### 4. 选择分析模式
- 输入1选择单次分析
- 输入2选择连续分析

### 5. 输入期号
- 单次分析：输入要分析的期号（如25001）
- 连续分析：输入开始分析的期号

## 输出结果

### 历史数据统计
- 各种比例分布的详细统计
- 出现次数和占比百分比

### 单期分析结果
- 目标期号的实际号码
- 历史概率分布排序和位置
- 跟随性概率分布排序和位置
- 最后排序位置数字

### 连续分析结果
- Excel文件保存
- 包含所有分析期的详细数据
- 汇总统计信息

## 文件说明

- `lottery_analysis_enhanced.py`：主程序文件
- `test_enhanced_analysis.py`：测试程序
- `lottery_data_all.xlsx`：数据文件（需要包含SSQ_data_all和DLT_data_all工作表）
- 输出文件：`{彩票类型}_连续分析结果_{时间戳}.xlsx`

## 依赖库

程序会自动检查并安装以下依赖库：
- pandas
- numpy
- openpyxl
- xlsxwriter

## 注意事项

1. 确保Excel数据文件存在且格式正确
2. 输入的期号必须在数据范围内
3. 分析需要足够的历史数据（至少10期）
4. 连续分析结果会自动保存到Excel文件

## 扩展性

程序采用模块化设计，便于后续添加新的分析功能：
- 可以轻松添加新的分析模块
- 可以修改现有分析算法
- 可以扩展预测功能
