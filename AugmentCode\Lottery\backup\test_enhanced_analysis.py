#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试增强版彩票分析程序
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lottery_analysis_enhanced import LotteryAnalyzerEnhanced

def test_analysis():
    """
    测试分析功能
    """
    print("=" * 60)
    print("测试增强版彩票分析程序")
    print("=" * 60)
    
    # 创建分析器实例
    analyzer = LotteryAnalyzerEnhanced()
    
    # 模拟用户选择
    print("模拟选择：1. SSQ (双色球)")
    analyzer.lottery_type = "SSQ"
    
    # 读取数据
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False
        
    # 设置数据
    analyzer.data = analyzer.ssqhistory_allout
    
    # 初始化分析模块
    analyzer.initialize_analysis_modules()
    
    # 测试历史数据统计
    print("\n测试历史数据统计分析（只显示前两项）...")
    analyzer.print_historical_statistics()
    
    # 测试单期分析
    print("\n测试单期分析...")
    if len(analyzer.data) > 100:
        # 选择一个中间的期号进行测试
        test_row = len(analyzer.data) // 2
        test_period = analyzer.data.iloc[test_row, 0]
        train_data = analyzer.data.iloc[:test_row].copy()
        
        result = analyzer.analyze_single_period(test_period, train_data)
        if result:
            print(f"单期分析测试成功，分析期号：{test_period}")
        else:
            print("单期分析测试失败")
            
    print("\n测试完成！")
    return True

if __name__ == "__main__":
    test_analysis()
