#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票数据分析增强版程序
基于Lottery_Final.py程序，增加详细的历史数据分析功能
包括奇偶比、质合比、大小比、和值走势等分析
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime
import threading
import sys
import os
import subprocess

def install_required_packages():
    """
    自动安装所需的Python库（静默安装）
    """
    required_packages = [
        'pandas',
        'numpy',
        'openpyxl',
        'xlsxwriter'
    ]

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package],
                                    stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            except subprocess.CalledProcessError:
                return False

    return True

class LotteryAnalysisModule:
    """
    彩票分析模块基类
    """
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        
    def analyze(self, data):
        """
        分析方法，子类需要实现
        """
        raise NotImplementedError("子类必须实现analyze方法")

class OddEvenAnalysisModule(LotteryAnalysisModule):
    """
    奇偶比分析模块
    """
    
    def analyze(self, data):
        """
        分析奇偶比分布
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)  # 红球列
            blue_columns = [7]  # 蓝球列
        else:  # DLT
            red_columns = range(1, 6)  # 红球列
            blue_columns = [6, 7]  # 蓝球列
            
        red_odd_even_stats = {}
        blue_odd_even_stats = {}
        
        # 分析红球奇偶比
        for _, row in data.iterrows():
            red_numbers = [row.iloc[col] for col in red_columns]
            odd_count = sum(1 for num in red_numbers if num % 2 == 1)
            even_count = len(red_numbers) - odd_count
            ratio_key = f"{odd_count}:{even_count}"
            red_odd_even_stats[ratio_key] = red_odd_even_stats.get(ratio_key, 0) + 1
            
        # 分析蓝球奇偶比
        for _, row in data.iterrows():
            blue_numbers = [row.iloc[col] for col in blue_columns]
            odd_count = sum(1 for num in blue_numbers if num % 2 == 1)
            even_count = len(blue_numbers) - odd_count
            ratio_key = f"{odd_count}:{even_count}"
            blue_odd_even_stats[ratio_key] = blue_odd_even_stats.get(ratio_key, 0) + 1
            
        # 计算占比
        total_periods = len(data)
        red_odd_even_ratios = {k: (v / total_periods * 100) for k, v in red_odd_even_stats.items()}
        blue_odd_even_ratios = {k: (v / total_periods * 100) for k, v in blue_odd_even_stats.items()}
        
        return {
            'red_odd_even_stats': red_odd_even_stats,
            'red_odd_even_ratios': red_odd_even_ratios,
            'blue_odd_even_stats': blue_odd_even_stats,
            'blue_odd_even_ratios': blue_odd_even_ratios
        }

class PrimeCompositeAnalysisModule(LotteryAnalysisModule):
    """
    质合比分析模块
    """
    
    def is_prime(self, n):
        """
        判断一个数是否为质数
        """
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False
        for i in range(3, int(n**0.5) + 1, 2):
            if n % i == 0:
                return False
        return True
    
    def analyze(self, data):
        """
        分析质合比分布
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)  # 红球列
            blue_columns = [7]  # 蓝球列
        else:  # DLT
            red_columns = range(1, 6)  # 红球列
            blue_columns = [6, 7]  # 蓝球列
            
        red_prime_composite_stats = {}
        blue_prime_composite_stats = {}
        
        # 分析红球质合比
        for _, row in data.iterrows():
            red_numbers = [row.iloc[col] for col in red_columns]
            prime_count = sum(1 for num in red_numbers if self.is_prime(num))
            composite_count = len(red_numbers) - prime_count
            ratio_key = f"{prime_count}:{composite_count}"
            red_prime_composite_stats[ratio_key] = red_prime_composite_stats.get(ratio_key, 0) + 1
            
        # 分析蓝球质合比
        for _, row in data.iterrows():
            blue_numbers = [row.iloc[col] for col in blue_columns]
            prime_count = sum(1 for num in blue_numbers if self.is_prime(num))
            composite_count = len(blue_numbers) - prime_count
            ratio_key = f"{prime_count}:{composite_count}"
            blue_prime_composite_stats[ratio_key] = blue_prime_composite_stats.get(ratio_key, 0) + 1
            
        # 计算占比
        total_periods = len(data)
        red_prime_composite_ratios = {k: (v / total_periods * 100) for k, v in red_prime_composite_stats.items()}
        blue_prime_composite_ratios = {k: (v / total_periods * 100) for k, v in blue_prime_composite_stats.items()}
        
        return {
            'red_prime_composite_stats': red_prime_composite_stats,
            'red_prime_composite_ratios': red_prime_composite_ratios,
            'blue_prime_composite_stats': blue_prime_composite_stats,
            'blue_prime_composite_ratios': blue_prime_composite_ratios
        }

class LargeSmalAnalysisModule(LotteryAnalysisModule):
    """
    大小比分析模块
    """
    
    def analyze(self, data):
        """
        分析大小比分布
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)  # 红球列
            blue_columns = [7]  # 蓝球列
            red_threshold = 16  # 红球大于16为大球
            blue_threshold = 8   # 蓝球大于8为大球
        else:  # DLT
            red_columns = range(1, 6)  # 红球列
            blue_columns = [6, 7]  # 蓝球列
            red_threshold = 17  # 红球大于17为大球
            blue_threshold = 6   # 蓝球大于6为大球
            
        red_large_small_stats = {}
        blue_large_small_stats = {}
        
        # 分析红球大小比
        for _, row in data.iterrows():
            red_numbers = [row.iloc[col] for col in red_columns]
            large_count = sum(1 for num in red_numbers if num > red_threshold)
            small_count = len(red_numbers) - large_count
            ratio_key = f"{large_count}:{small_count}"
            red_large_small_stats[ratio_key] = red_large_small_stats.get(ratio_key, 0) + 1
            
        # 分析蓝球大小比
        for _, row in data.iterrows():
            blue_numbers = [row.iloc[col] for col in blue_columns]
            large_count = sum(1 for num in blue_numbers if num > blue_threshold)
            small_count = len(blue_numbers) - large_count
            ratio_key = f"{large_count}:{small_count}"
            blue_large_small_stats[ratio_key] = blue_large_small_stats.get(ratio_key, 0) + 1
            
        # 计算占比
        total_periods = len(data)
        red_large_small_ratios = {k: (v / total_periods * 100) for k, v in red_large_small_stats.items()}
        blue_large_small_ratios = {k: (v / total_periods * 100) for k, v in blue_large_small_stats.items()}
        
        return {
            'red_large_small_stats': red_large_small_stats,
            'red_large_small_ratios': red_large_small_ratios,
            'blue_large_small_stats': blue_large_small_stats,
            'blue_large_small_ratios': blue_large_small_ratios
        }

class SumTrendAnalysisModule(LotteryAnalysisModule):
    """
    和值走势分析模块
    """

    def analyze(self, data):
        """
        分析和值走势分布
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)  # 红球列
            blue_columns = [7]  # 蓝球列
        else:  # DLT
            red_columns = range(1, 6)  # 红球列
            blue_columns = [6, 7]  # 蓝球列

        red_sum_stats = {}
        blue_sum_stats = {}

        # 分析红球和值分布
        for _, row in data.iterrows():
            red_numbers = [row.iloc[col] for col in red_columns]
            red_sum = sum(red_numbers)
            # 按每10个数为一个区间统计
            interval = (red_sum // 10) * 10
            interval_key = f"{interval}-{interval + 9}"
            red_sum_stats[interval_key] = red_sum_stats.get(interval_key, 0) + 1

        # 分析蓝球和值分布
        for _, row in data.iterrows():
            blue_numbers = [row.iloc[col] for col in blue_columns]
            blue_sum = sum(blue_numbers)
            # 如果只有一个蓝球，和值就是号码本身
            if len(blue_numbers) == 1:
                interval = (blue_sum // 10) * 10
                interval_key = f"{interval}-{interval + 9}"
            else:
                interval = (blue_sum // 10) * 10
                interval_key = f"{interval}-{interval + 9}"
            blue_sum_stats[interval_key] = blue_sum_stats.get(interval_key, 0) + 1

        # 计算占比
        total_periods = len(data)
        red_sum_ratios = {k: (v / total_periods * 100) for k, v in red_sum_stats.items()}
        blue_sum_ratios = {k: (v / total_periods * 100) for k, v in blue_sum_stats.items()}

        return {
            'red_sum_stats': red_sum_stats,
            'red_sum_ratios': red_sum_ratios,
            'blue_sum_stats': blue_sum_stats,
            'blue_sum_ratios': blue_sum_ratios
        }

class HistoricalProbabilityModule(LotteryAnalysisModule):
    """
    历史概率分布分析模块
    """

    def analyze(self, data):
        """
        分析历史出现概率分布
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)  # 红球列
            blue_columns = [7]  # 蓝球列
            red_range = (1, 33)
            blue_range = (1, 16)
        else:  # DLT
            red_columns = range(1, 6)  # 红球列
            blue_columns = [6, 7]  # 蓝球列
            red_range = (1, 35)
            blue_range = (1, 12)

        # 统计红球出现次数
        red_counts = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        for _, row in data.iterrows():
            for col in red_columns:
                ball = row.iloc[col]
                if red_range[0] <= ball <= red_range[1]:
                    red_counts[ball] += 1

        # 统计蓝球出现次数
        blue_counts = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}
        for _, row in data.iterrows():
            for col in blue_columns:
                ball = row.iloc[col]
                if blue_range[0] <= ball <= blue_range[1]:
                    blue_counts[ball] += 1

        # 计算概率
        total_red_draws = len(data) * len(red_columns)
        total_blue_draws = len(data) * len(blue_columns)

        red_probabilities = {ball: (count / total_red_draws * 100) for ball, count in red_counts.items()}
        blue_probabilities = {ball: (count / total_blue_draws * 100) for ball, count in blue_counts.items()}

        # 按概率从大到小排序
        red_sorted = sorted(red_probabilities.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probabilities.items(), key=lambda x: x[1], reverse=True)

        return {
            'red_counts': red_counts,
            'red_probabilities': red_probabilities,
            'red_sorted': red_sorted,
            'blue_counts': blue_counts,
            'blue_probabilities': blue_probabilities,
            'blue_sorted': blue_sorted
        }

class FollowingProbabilityModule(LotteryAnalysisModule):
    """
    跟随性概率分析模块（多条件贝叶斯）
    """

    def calculate_ball_statistics(self, data, columns, min_ball, max_ball):
        """
        统计球号出现次数和概率
        """
        times = {i: 0 for i in range(min_ball, max_ball + 1)}

        all_balls = np.array([])
        for col in columns:
            all_balls = np.append(all_balls, data.iloc[:, col].values)

        for ball in all_balls:
            if min_ball <= ball <= max_ball:
                times[int(ball)] += 1

        total_count = sum(times.values())
        probs = {ball: count / total_count if total_count > 0 else 0 for ball, count in times.items()}

        return times, probs

    def calculate_follow_statistics(self, data, columns, min_ball, max_ball):
        """
        计算球号的跟随性统计
        """
        num_balls = max_ball - min_ball + 1
        follow_time = np.zeros((num_balls, num_balls), dtype=int)
        follow_prob = np.zeros((num_balls, num_balls), dtype=float)

        for i in range(len(data) - 1):
            current_row_balls = set()
            next_row_balls = set()

            for col in columns:
                ball = data.iloc[i, col]
                if min_ball <= ball <= max_ball:
                    current_row_balls.add(int(ball))

            for col in columns:
                ball = data.iloc[i+1, col]
                if min_ball <= ball <= max_ball:
                    next_row_balls.add(int(ball))

            for current_ball in current_row_balls:
                for next_ball in next_row_balls:
                    follow_time[next_ball - min_ball, current_ball - min_ball] += 1

        for col in range(num_balls):
            col_sum = np.sum(follow_time[:, col])
            if col_sum > 0:
                follow_prob[:, col] = follow_time[:, col] / col_sum

        return follow_time, follow_prob

    def analyze(self, data, previous_period_data=None):
        """
        分析跟随性概率分布（基于上一期号码的多条件贝叶斯预测概率）
        """
        if previous_period_data is None:
            # 如果没有指定上一期数据，使用最后一期作为基准
            previous_period_data = data.iloc[-1]

        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)  # 红球列
            blue_columns = [7]  # 蓝球列
            red_range = (1, 33)
            blue_range = (1, 16)
        else:  # DLT
            red_columns = range(1, 6)  # 红球列
            blue_columns = [6, 7]  # 蓝球列
            red_range = (1, 35)
            blue_range = (1, 12)

        # 统计红球和蓝球的出现次数和概率
        _, red_probs = self.calculate_ball_statistics(
            data, red_columns, red_range[0], red_range[1])
        _, blue_probs = self.calculate_ball_statistics(
            data, blue_columns, blue_range[0], blue_range[1])

        # 统计跟随性
        _, red_follow_prob = self.calculate_follow_statistics(
            data, red_columns, red_range[0], red_range[1])
        _, blue_follow_prob = self.calculate_follow_statistics(
            data, blue_columns, blue_range[0], blue_range[1])

        # 获取上一期号码
        last_numbers = previous_period_data.iloc[1:].values

        # 红球多条件贝叶斯概率
        red_prediction_probs = {}
        red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
        for i in range(red_range[1] - red_range[0] + 1):
            prob_sum = 0
            for j, col in enumerate(red_columns):
                last_ball = last_numbers[col - 1]
                if red_range[0] <= last_ball <= red_range[1]:
                    prob_sum += red_follow_prob[i, last_ball - red_range[0]] * red_probs_list[last_ball - red_range[0]]
            red_prediction_probs[i + red_range[0]] = prob_sum

        # 蓝球多条件贝叶斯概率
        blue_prediction_probs = {}
        blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
        for i in range(blue_range[1] - blue_range[0] + 1):
            prob_sum = 0
            for j, col in enumerate(blue_columns):
                last_ball = last_numbers[col - 1]
                if blue_range[0] <= last_ball <= blue_range[1]:
                    prob_sum += blue_follow_prob[i, last_ball - blue_range[0]] * blue_probs_list[last_ball - blue_range[0]]
            blue_prediction_probs[i + blue_range[0]] = prob_sum

        # 按概率从大到小排序
        red_sorted = sorted(red_prediction_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_prediction_probs.items(), key=lambda x: x[1], reverse=True)

        return {
            'red_prediction_probs': red_prediction_probs,
            'red_sorted': red_sorted,
            'blue_prediction_probs': blue_prediction_probs,
            'blue_sorted': blue_sorted
        }

class LotteryAnalyzerEnhanced:
    """
    增强版彩票分析器
    """

    def __init__(self, file_path="lottery_data_all.xlsx"):
        """
        初始化彩票分析器
        """
        self.file_path = file_path
        self.ssqhistory_allout = None
        self.dlthistory_allout = None
        self.lottery_type = None
        self.data = None
        self.operation_mode = None
        self.analysis_mode = None

        # 初始化分析模块
        self.analysis_modules = {}

    def initialize_analysis_modules(self):
        """
        初始化分析模块
        """
        self.analysis_modules = {
            'odd_even': OddEvenAnalysisModule(self.lottery_type),
            'prime_composite': PrimeCompositeAnalysisModule(self.lottery_type),
            'large_small': LargeSmalAnalysisModule(self.lottery_type),
            'sum_trend': SumTrendAnalysisModule(self.lottery_type),
            'historical_probability': HistoricalProbabilityModule(self.lottery_type),
            'following_probability': FollowingProbabilityModule(self.lottery_type)
        }

    def get_user_input(self, prompt):
        """
        获取用户输入
        """
        try:
            user_input = input(prompt)
            return user_input.strip()
        except KeyboardInterrupt:
            print("\n用户中断程序")
            return ""

    def read_and_sort_data(self):
        """
        根据选择的彩票类型读取相应的Excel表格数据并排序
        """
        print(f"正在读取{self.lottery_type}数据并排序...")
        print("=" * 60)

        try:
            if self.lottery_type == "SSQ":
                print("正在读取双色球数据...")
                ssq_data = pd.read_excel(self.file_path, sheet_name="SSQ_data_all")
                self.ssqhistory_allout = ssq_data.iloc[:, [0] + list(range(8, 15))].copy()
                dataset = self.ssqhistory_allout
                dataset_name = "双色球(SSQ)"
            else:  # DLT
                print("正在读取大乐透数据...")
                dlt_data = pd.read_excel(self.file_path, sheet_name="DLT_data_all")
                self.dlthistory_allout = dlt_data.iloc[:, [0] + list(range(7, 14))].copy()
                dataset = self.dlthistory_allout
                dataset_name = "大乐透(DLT)"

            # 删除包含NaN的行
            dataset.dropna(inplace=True)
            # 按第一列（NO列）从小到大排序
            dataset.sort_values(by=dataset.columns[0], inplace=True)
            # 重置索引
            dataset.reset_index(drop=True, inplace=True)

            # 确保数据类型一致性
            try:
                dataset.iloc[:, 0] = dataset.iloc[:, 0].astype(int)
                for col in range(1, dataset.shape[1]):
                    dataset.iloc[:, col] = dataset.iloc[:, col].astype(int)
            except ValueError as e:
                print(f"警告: 转换 {dataset_name} 的数据类型时出错: {e}")
                for col in range(dataset.shape[1]):
                    dataset.iloc[:, col] = dataset.iloc[:, col].fillna(0).astype(int)

            print(f"\n{dataset_name} 数据信息:")
            print(f"行数: {dataset.shape[0]}, 列数: {dataset.shape[1]}")
            print(f"期号范围: {dataset.iloc[0, 0]} - {dataset.iloc[-1, 0]}")
            print(f"\n{dataset_name}数据读取和排序成功！")
            return True

        except FileNotFoundError:
            print(f"错误: 找不到文件 '{self.file_path}'，请确保文件存在。")
            return False
        except Exception as e:
            print(f"错误: {str(e)}")
            return False

    def step1_select_lottery_type(self):
        """
        第一步：选择彩票类型
        """
        print("=" * 60)
        print("第一步：选择彩票类型")
        print("=" * 60)

        print("\n请选择彩票类型:")
        print("1. SSQ (双色球)")
        print("2. DLT (大乐透)")

        lottery_choice = self.get_user_input("请输入选择 (1 或 2): ")

        if lottery_choice == "2":
            self.lottery_type = "DLT"
            print("您选择了：大乐透 (DLT)")
        else:
            self.lottery_type = "SSQ"
            print("您选择了：双色球 (SSQ)")

        print("\n第一步完成：彩票类型选择成功！")
        return True

    def step2_select_operation_mode(self):
        """
        第二步：选择操作模式
        """
        print("\n" + "=" * 60)
        print("第二步：选择操作模式")
        print("=" * 60)

        print("\n请选择操作模式:")
        print("1. 预测")
        print("2. 分析")

        mode_choice = self.get_user_input("请输入选择 (1 或 2): ")

        if mode_choice == "2":
            self.operation_mode = "analysis"
            print("您选择了：分析模式")
            return self.select_analysis_mode()
        else:
            self.operation_mode = "prediction"
            print("您选择了：预测模式")
            print("预测功能暂时留空，后续增加。")
            return False

    def select_analysis_mode(self):
        """
        选择分析模式的子选项
        """
        print("\n请选择分析模式:")
        print("1. 单次分析")
        print("2. 连续分析")

        analysis_choice = self.get_user_input("请输入选择 (1 或 2): ")

        if analysis_choice == "2":
            self.analysis_mode = "continuous"
            print("您选择了：连续分析")
        else:
            self.analysis_mode = "single"
            print("您选择了：单次分析")

        print("\n第二步完成：操作模式选择成功！")
        return True

    def print_historical_statistics(self):
        """
        打印历史数据统计分析结果
        """
        print("\n" + "=" * 80)
        print("历史数据统计分析")
        print("=" * 80)

        # 奇偶比分析
        odd_even_result = self.analysis_modules['odd_even'].analyze(self.data)
        print(f"\n【{self.lottery_type}奇偶比分析】")
        print("红球奇偶比分布:")
        red_odd_even_sorted = sorted(odd_even_result['red_odd_even_ratios'].items(), key=lambda x: x[1], reverse=True)
        for i, (ratio, percentage) in enumerate(red_odd_even_sorted[:2]):
            count = odd_even_result['red_odd_even_stats'][ratio]
            print(f"  {ratio} - {count}次 ({percentage:.2f}%)")
        print("蓝球奇偶比分布:")
        blue_odd_even_sorted = sorted(odd_even_result['blue_odd_even_ratios'].items(), key=lambda x: x[1], reverse=True)
        for i, (ratio, percentage) in enumerate(blue_odd_even_sorted[:2]):
            count = odd_even_result['blue_odd_even_stats'][ratio]
            print(f"  {ratio} - {count}次 ({percentage:.2f}%)")

        # 质合比分析
        prime_composite_result = self.analysis_modules['prime_composite'].analyze(self.data)
        print(f"\n【{self.lottery_type}质合比分析】")
        print("红球质合比分布:")
        red_prime_composite_sorted = sorted(prime_composite_result['red_prime_composite_ratios'].items(), key=lambda x: x[1], reverse=True)
        for i, (ratio, percentage) in enumerate(red_prime_composite_sorted[:2]):
            count = prime_composite_result['red_prime_composite_stats'][ratio]
            print(f"  {ratio} - {count}次 ({percentage:.2f}%)")
        print("蓝球质合比分布:")
        blue_prime_composite_sorted = sorted(prime_composite_result['blue_prime_composite_ratios'].items(), key=lambda x: x[1], reverse=True)
        for i, (ratio, percentage) in enumerate(blue_prime_composite_sorted[:2]):
            count = prime_composite_result['blue_prime_composite_stats'][ratio]
            print(f"  {ratio} - {count}次 ({percentage:.2f}%)")

        # 大小比分析
        large_small_result = self.analysis_modules['large_small'].analyze(self.data)
        print(f"\n【{self.lottery_type}大小比分析】")
        if self.lottery_type == "SSQ":
            print("红球大小比分布（大球>16）:")
        else:
            print("红球大小比分布（大球>17）:")
        red_large_small_sorted = sorted(large_small_result['red_large_small_ratios'].items(), key=lambda x: x[1], reverse=True)
        for i, (ratio, percentage) in enumerate(red_large_small_sorted[:2]):
            count = large_small_result['red_large_small_stats'][ratio]
            print(f"  {ratio} - {count}次 ({percentage:.2f}%)")
        if self.lottery_type == "SSQ":
            print("蓝球大小比分布（大球>8）:")
        else:
            print("蓝球大小比分布（大球>6）:")
        blue_large_small_sorted = sorted(large_small_result['blue_large_small_ratios'].items(), key=lambda x: x[1], reverse=True)
        for i, (ratio, percentage) in enumerate(blue_large_small_sorted[:2]):
            count = large_small_result['blue_large_small_stats'][ratio]
            print(f"  {ratio} - {count}次 ({percentage:.2f}%)")

        # 和值走势分析
        sum_trend_result = self.analysis_modules['sum_trend'].analyze(self.data)
        print(f"\n【{self.lottery_type}和值走势分析】")
        print("红球和值区间分布:")
        red_sum_sorted = sorted(sum_trend_result['red_sum_ratios'].items(), key=lambda x: x[1], reverse=True)
        for i, (interval, percentage) in enumerate(red_sum_sorted[:2]):
            count = sum_trend_result['red_sum_stats'][interval]
            print(f"  {interval} - {count}次 ({percentage:.2f}%)")
        print("蓝球和值区间分布:")
        blue_sum_sorted = sorted(sum_trend_result['blue_sum_ratios'].items(), key=lambda x: x[1], reverse=True)
        for i, (interval, percentage) in enumerate(blue_sum_sorted[:2]):
            count = sum_trend_result['blue_sum_stats'][interval]
            print(f"  {interval} - {count}次 ({percentage:.2f}%)")

    def analyze_single_period(self, target_period, train_data):
        """
        分析单个期号
        """
        print(f"\n" + "=" * 80)
        print(f"分析第 {target_period} 期")
        print("=" * 80)

        # 获取目标期号的实际号码
        target_row = None
        for i, row in self.data.iterrows():
            if row.iloc[0] == target_period:
                target_row = i
                break

        if target_row is None:
            print(f"未找到期号 {target_period}")
            return None

        target_numbers = self.data.iloc[target_row, 1:].tolist()
        if self.lottery_type == "SSQ":
            target_red = target_numbers[:6]
            target_blue = target_numbers[6:7]
        else:
            target_red = target_numbers[:5]
            target_blue = target_numbers[5:7]

        print(f"第{target_period}期实际号码：")
        print(f"红球：{target_red}")
        print(f"蓝球：{target_blue}")

        # a. 历史出现概率分布分析
        historical_result = self.analysis_modules['historical_probability'].analyze(train_data)
        print(f"\n【a. 历史出现概率分布】")
        print("红球历史概率排序（从大到小）：")
        for i, (ball, prob) in enumerate(historical_result['red_sorted'], 1):
            print(f"  {i:2d}. 号码 {ball:2d}: {prob:.4f}%")
        print("蓝球历史概率排序（从大到小）：")
        for i, (ball, prob) in enumerate(historical_result['blue_sorted'], 1):
            print(f"  {i:2d}. 号码 {ball:2d}: {prob:.4f}%")

        # 计算目标期号码在历史概率排序中的位置
        red_positions = []
        for ball in target_red:
            for i, (sorted_ball, _) in enumerate(historical_result['red_sorted'], 1):
                if sorted_ball == ball:
                    red_positions.append(i)
                    break
        blue_positions = []
        for ball in target_blue:
            for i, (sorted_ball, _) in enumerate(historical_result['blue_sorted'], 1):
                if sorted_ball == ball:
                    blue_positions.append(i)
                    break

        print(f"\n第{target_period}期红球在历史概率排序中的位置：{red_positions}")
        print(f"红球最后排序位置：{max(red_positions) if red_positions else 'N/A'}")
        print(f"第{target_period}期蓝球在历史概率排序中的位置：{blue_positions}")
        print(f"蓝球最后排序位置：{max(blue_positions) if blue_positions else 'N/A'}")

        # b. 跟随性概率分布分析
        if target_row > 0:
            previous_period_data = self.data.iloc[target_row - 1]
            following_result = self.analysis_modules['following_probability'].analyze(train_data, previous_period_data)
            print(f"\n【b. 跟随性概率分布（基于第{previous_period_data.iloc[0]}期）】")
            print("红球跟随性概率排序（从大到小）：")
            for i, (ball, prob) in enumerate(following_result['red_sorted'], 1):
                print(f"  {i:2d}. 号码 {ball:2d}: {prob*100:.4f}%")
            print("蓝球跟随性概率排序（从大到小）：")
            for i, (ball, prob) in enumerate(following_result['blue_sorted'], 1):
                print(f"  {i:2d}. 号码 {ball:2d}: {prob*100:.4f}%")

            # 计算目标期号码在跟随性概率排序中的位置
            red_follow_positions = []
            for ball in target_red:
                for i, (sorted_ball, _) in enumerate(following_result['red_sorted'], 1):
                    if sorted_ball == ball:
                        red_follow_positions.append(i)
                        break
            blue_follow_positions = []
            for ball in target_blue:
                for i, (sorted_ball, _) in enumerate(following_result['blue_sorted'], 1):
                    if sorted_ball == ball:
                        blue_follow_positions.append(i)
                        break

            print(f"\n第{target_period}期红球在跟随性概率排序中的位置：{red_follow_positions}")
            print(f"红球最后排序位置：{max(red_follow_positions) if red_follow_positions else 'N/A'}")
            print(f"第{target_period}期蓝球在跟随性概率排序中的位置：{blue_follow_positions}")
            print(f"蓝球最后排序位置：{max(blue_follow_positions) if blue_follow_positions else 'N/A'}")

        return {
            'period': target_period,
            'red_numbers': target_red,
            'blue_numbers': target_blue,
            'red_historical_positions': red_positions,
            'blue_historical_positions': blue_positions,
            'red_following_positions': red_follow_positions if target_row > 0 else [],
            'blue_following_positions': blue_follow_positions if target_row > 0 else []
        }

    def analyze_single_period_for_continuous(self, target_period, train_data):
        """
        为连续分析专门设计的单期分析方法（不打印详细概率排序）
        """
        print(f"\n" + "=" * 80)
        print(f"分析第 {target_period} 期")
        print("=" * 80)

        # 获取目标期号的实际号码
        target_row = None
        for i, row in self.data.iterrows():
            if row.iloc[0] == target_period:
                target_row = i
                break

        if target_row is None:
            print(f"未找到期号 {target_period}")
            return None

        target_numbers = self.data.iloc[target_row, 1:].tolist()
        if self.lottery_type == "SSQ":
            target_red = target_numbers[:6]
            target_blue = target_numbers[6:7]
        else:
            target_red = target_numbers[:5]
            target_blue = target_numbers[5:7]

        print(f"第{target_period}期实际号码：")
        print(f"红球：{target_red}")
        print(f"蓝球：{target_blue}")

        # a. 历史出现概率分布分析（不打印详细排序）
        historical_result = self.analysis_modules['historical_probability'].analyze(train_data)

        # 计算目标期号码在历史概率排序中的位置
        red_positions = []
        for ball in target_red:
            for i, (sorted_ball, _) in enumerate(historical_result['red_sorted'], 1):
                if sorted_ball == ball:
                    red_positions.append(i)
                    break
        blue_positions = []
        for ball in target_blue:
            for i, (sorted_ball, _) in enumerate(historical_result['blue_sorted'], 1):
                if sorted_ball == ball:
                    blue_positions.append(i)
                    break

        print(f"\n第{target_period}期红球在历史概率排序中的位置：{red_positions}")
        print(f"红球最后排序位置：{max(red_positions) if red_positions else 'N/A'}")
        print(f"第{target_period}期蓝球在历史概率排序中的位置：{blue_positions}")
        print(f"蓝球最后排序位置：{max(blue_positions) if blue_positions else 'N/A'}")

        # b. 跟随性概率分布分析（不打印详细排序）
        red_follow_positions = []
        blue_follow_positions = []
        if target_row > 0:
            previous_period_data = self.data.iloc[target_row - 1]
            following_result = self.analysis_modules['following_probability'].analyze(train_data, previous_period_data)
            print(f"\n基于第{previous_period_data.iloc[0]}期的跟随性概率分析：")

            # 计算目标期号码在跟随性概率排序中的位置
            for ball in target_red:
                for i, (sorted_ball, _) in enumerate(following_result['red_sorted'], 1):
                    if sorted_ball == ball:
                        red_follow_positions.append(i)
                        break
            for ball in target_blue:
                for i, (sorted_ball, _) in enumerate(following_result['blue_sorted'], 1):
                    if sorted_ball == ball:
                        blue_follow_positions.append(i)
                        break

            print(f"第{target_period}期红球在跟随性概率排序中的位置：{red_follow_positions}")
            print(f"红球最后排序位置：{max(red_follow_positions) if red_follow_positions else 'N/A'}")
            print(f"第{target_period}期蓝球在跟随性概率排序中的位置：{blue_follow_positions}")
            print(f"蓝球最后排序位置：{max(blue_follow_positions) if blue_follow_positions else 'N/A'}")

        return {
            'period': target_period,
            'red_numbers': target_red,
            'blue_numbers': target_blue,
            'red_historical_positions': red_positions,
            'blue_historical_positions': blue_positions,
            'red_following_positions': red_follow_positions,
            'blue_following_positions': blue_follow_positions
        }

    def single_analysis(self):
        """
        单次分析：分析指定期号
        """
        # 首先打印历史数据统计
        self.print_historical_statistics()

        # 循环询问用户要分析的期号，直到输入正确
        while True:
            period_input = self.get_user_input("\n请输入要分析的期号（例如：25001）: ")

            if not period_input:
                print("输入错误，请重新输入期号")
                continue

            try:
                target_period = int(period_input)
            except ValueError:
                print("输入错误，请重新输入期号（请输入数字）")
                continue

            # 查找目标期号在数据中的位置
            target_row = None
            for i, row in self.data.iterrows():
                if row.iloc[0] == target_period:
                    target_row = i
                    break

            if target_row is None:
                print(f"输入错误，请重新输入期号（未找到期号 {target_period}）")
                continue

            # 输入正确，跳出循环
            break

        # 使用目标期号之前的数据作为训练数据
        train_data = self.data.iloc[:target_row].copy()

        if len(train_data) < 10:
            print("训练数据不足，无法进行分析")
            return False

        # 进行单期分析
        result = self.analyze_single_period(target_period, train_data)
        return result is not None

    def continuous_analysis(self):
        """
        连续分析：从指定期号开始连续分析最多500期
        """
        # 首先打印历史数据统计
        self.print_historical_statistics()

        # 循环询问用户开始期号，直到输入正确
        while True:
            period_input = self.get_user_input("\n请输入开始分析的期号（例如：25001）: ")

            if not period_input:
                print("输入错误，请重新输入期号")
                continue

            try:
                start_period = int(period_input)
            except ValueError:
                print("输入错误，请重新输入期号（请输入数字）")
                continue

            # 查找开始期号在数据中的位置
            start_row = None
            for i, row in self.data.iterrows():
                if row.iloc[0] == start_period:
                    start_row = i
                    break

            if start_row is None:
                print(f"输入错误，请重新输入期号（未找到期号 {start_period}）")
                continue

            # 输入正确，跳出循环
            break

        print(f"开始连续分析，从第 {start_period} 期开始...")

        # 存储分析结果
        analysis_results = []

        # 最多分析500期
        for i in range(500):
            current_row = start_row + i

            # 检查是否超出数据范围
            if current_row >= len(self.data):
                print(f"已到达数据末尾，停止分析")
                break

            current_period = self.data.iloc[current_row, 0]

            # 使用当前期号之前的数据作为训练数据
            train_data = self.data.iloc[:current_row].copy()

            if len(train_data) < 10:
                print(f"第 {current_period} 期训练数据不足，跳过")
                continue

            # 进行单期分析（连续分析模式，不打印详细信息）
            result = self.analyze_single_period_for_continuous(current_period, train_data)
            if result:
                result['sequence'] = i + 1
                analysis_results.append(result)

            # 显示进度
            if (i + 1) % 50 == 0:
                print(f"已完成 {i + 1} 期分析...")

        print(f"\n连续分析完成！共分析了 {len(analysis_results)} 期")

        # 检查是否因为到达数据末尾而停止
        reached_end = (start_row + len(analysis_results)) >= len(self.data)

        if reached_end:
            print("已到达数据末尾，自动保存分析结果...")
            # 自动保存结果到Excel
            return self.save_continuous_analysis_results(analysis_results)
        else:
            # 询问用户是否保存连续分析结果
            continue_choice = self.get_user_input("是否保存连续分析结果到Excel文件？(y/n): ")
            if continue_choice.lower() == 'y':
                # 保存结果到Excel
                return self.save_continuous_analysis_results(analysis_results)
            else:
                return True

    def save_continuous_analysis_results(self, results):
        """
        保存连续分析结果到Excel文件
        """
        if not results:
            print("没有分析结果可保存")
            return False

        try:
            # 创建结果DataFrame
            data_for_df = []
            for result in results:
                # 格式化红球和蓝球号码显示
                red_balls_str = '+'.join([f"{ball:02d}" for ball in result['red_numbers']])
                blue_balls_str = '+'.join([f"{ball:02d}" for ball in result['blue_numbers']])

                row_data = {
                    '分析序号': result['sequence'],
                    '分析期号': result['period'],
                    '红球号码': red_balls_str,
                    '蓝球号码': blue_balls_str,
                    '红球历史概率位置': str(result['red_historical_positions']),
                    '红球历史概率最后位置': max(result['red_historical_positions']) if result['red_historical_positions'] else 'N/A',
                    '蓝球历史概率位置': str(result['blue_historical_positions']),
                    '蓝球历史概率最后位置': max(result['blue_historical_positions']) if result['blue_historical_positions'] else 'N/A',
                    '红球跟随性概率位置': str(result['red_following_positions']),
                    '红球跟随性概率最后位置': max(result['red_following_positions']) if result['red_following_positions'] else 'N/A',
                    '蓝球跟随性概率位置': str(result['blue_following_positions']),
                    '蓝球跟随性概率最后位置': max(result['blue_following_positions']) if result['blue_following_positions'] else 'N/A'
                }
                data_for_df.append(row_data)

            results_df = pd.DataFrame(data_for_df)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.lottery_type}_连续分析结果_{timestamp}.xlsx"

            # 保存到Excel文件
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                results_df.to_excel(writer, sheet_name='连续分析结果', index=False)

                # 创建汇总统计
                total_analysis = len(results)
                summary_data = {
                    '统计项目': ['彩票类型', '总分析期数', '开始期号', '结束期号'],
                    '数值': [self.lottery_type, total_analysis,
                           results[0]['period'] if results else 'N/A',
                           results[-1]['period'] if results else 'N/A']
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

            print(f"连续分析结果已保存到文件: {filename}")
            return True

        except Exception as e:
            print(f"保存结果时出错: {str(e)}")
            return False

    def run_analysis(self):
        """
        运行完整的分析流程
        """
        print("开始彩票数据分析增强版程序")
        print("=" * 60)

        # 第一步：选择彩票类型
        if not self.step1_select_lottery_type():
            return False

        # 读取数据（根据选择的彩票类型）
        if not self.read_and_sort_data():
            return False

        # 根据彩票类型设置数据
        if self.lottery_type == "DLT":
            self.data = self.dlthistory_allout
        else:
            self.data = self.ssqhistory_allout

        # 初始化分析模块
        self.initialize_analysis_modules()

        # 第二步：选择操作模式
        if not self.step2_select_operation_mode():
            return False

        # 根据选择的模式执行相应操作
        if self.operation_mode == "analysis":
            if self.analysis_mode == "single":
                return self.single_analysis()
            elif self.analysis_mode == "continuous":
                return self.continuous_analysis()

        print("\n" + "=" * 60)
        print("程序执行完成！")
        print("=" * 60)

        return True

def main():
    """
    主函数
    """
    # 静默安装所需的Python库
    install_required_packages()

    # 创建分析器实例
    analyzer = LotteryAnalyzerEnhanced()

    # 运行完整分析流程
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
