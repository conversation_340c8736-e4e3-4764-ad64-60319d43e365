#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试连续分析功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lottery_analysis_enhanced import LotteryAnalyzerEnhanced

def test_continuous_analysis():
    """
    测试连续分析功能
    """
    print("=" * 80)
    print("测试连续分析功能")
    print("=" * 80)
    
    # 创建分析器实例
    analyzer = LotteryAnalyzerEnhanced()
    
    # 设置为SSQ
    analyzer.lottery_type = "SSQ"
    
    # 读取数据
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False
        
    # 设置数据
    analyzer.data = analyzer.ssqhistory_allout
    
    # 初始化分析模块
    analyzer.initialize_analysis_modules()
    
    # 打印历史数据统计（只显示前两项）
    print("\n历史数据统计分析（只显示前两项）...")
    analyzer.print_historical_statistics()
    
    # 模拟连续分析（测试前5期）
    if len(analyzer.data) > 110:
        start_row = len(analyzer.data) - 110
        analysis_results = []
        
        print(f"\n模拟连续分析，从第 {analyzer.data.iloc[start_row, 0]} 期开始（前5期）...")
        
        for i in range(5):
            current_row = start_row + i
            current_period = analyzer.data.iloc[current_row, 0]
            train_data = analyzer.data.iloc[:current_row].copy()
            
            if len(train_data) >= 10:
                # 使用新的连续分析方法
                result = analyzer.analyze_single_period_for_continuous(current_period, train_data)
                if result:
                    result['sequence'] = i + 1
                    analysis_results.append(result)
        
        print(f"\n连续分析测试完成！共分析了 {len(analysis_results)} 期")
        
        # 测试保存功能
        if analysis_results:
            print("测试保存功能...")
            success = analyzer.save_continuous_analysis_results(analysis_results)
            if success:
                print("保存功能测试成功！")
            else:
                print("保存功能测试失败！")
        
    return True

if __name__ == "__main__":
    test_continuous_analysis()
