#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
演示增强版彩票分析程序的功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lottery_analysis_enhanced import LotteryAnalyzerEnhanced

def demo_single_analysis():
    """
    演示单期分析功能
    """
    print("=" * 80)
    print("演示：单期分析功能")
    print("=" * 80)
    
    # 创建分析器实例
    analyzer = LotteryAnalyzerEnhanced()
    
    # 设置为SSQ
    analyzer.lottery_type = "SSQ"
    
    # 读取数据
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False
        
    # 设置数据
    analyzer.data = analyzer.ssqhistory_allout
    
    # 初始化分析模块
    analyzer.initialize_analysis_modules()
    
    # 打印历史数据统计（只显示前两项）
    print("\n历史数据统计分析（按百分比从大到小排序，只显示前两项）...")
    analyzer.print_historical_statistics()
    
    # 选择一个测试期号进行分析
    if len(analyzer.data) > 100:
        test_row = len(analyzer.data) // 2
        test_period = analyzer.data.iloc[test_row, 0]
        train_data = analyzer.data.iloc[:test_row].copy()
        
        print(f"\n演示分析第 {test_period} 期...")
        result = analyzer.analyze_single_period(test_period, train_data)
        
        if result:
            print(f"\n分析结果摘要：")
            print(f"期号：{result['period']}")
            print(f"红球：{result['red_numbers']}")
            print(f"蓝球：{result['blue_numbers']}")
            print(f"红球历史概率最后排序位置：{max(result['red_historical_positions']) if result['red_historical_positions'] else 'N/A'}")
            print(f"蓝球历史概率最后排序位置：{max(result['blue_historical_positions']) if result['blue_historical_positions'] else 'N/A'}")
            print(f"红球跟随性最后排序位置：{max(result['red_following_positions']) if result['red_following_positions'] else 'N/A'}")
            print(f"蓝球跟随性最后排序位置：{max(result['blue_following_positions']) if result['blue_following_positions'] else 'N/A'}")
            
    print("\n单期分析演示完成！")
    return True

def demo_continuous_analysis():
    """
    演示连续分析功能（模拟）
    """
    print("\n" + "=" * 80)
    print("演示：连续分析功能（模拟前10期）")
    print("=" * 80)
    
    # 创建分析器实例
    analyzer = LotteryAnalyzerEnhanced()
    
    # 设置为SSQ
    analyzer.lottery_type = "SSQ"
    
    # 读取数据
    if not analyzer.read_and_sort_data():
        print("数据读取失败")
        return False
        
    # 设置数据
    analyzer.data = analyzer.ssqhistory_allout
    
    # 初始化分析模块
    analyzer.initialize_analysis_modules()
    
    # 模拟连续分析前10期
    if len(analyzer.data) > 110:
        start_row = len(analyzer.data) - 110
        analysis_results = []
        
        print(f"模拟连续分析，从第 {analyzer.data.iloc[start_row, 0]} 期开始（前3期，不显示详细概率排序）...")
        
        for i in range(3):  # 只演示3期，避免输出过长
            current_row = start_row + i
            current_period = analyzer.data.iloc[current_row, 0]
            train_data = analyzer.data.iloc[:current_row].copy()

            if len(train_data) >= 10:
                # 使用新的连续分析方法（不打印详细概率排序）
                result = analyzer.analyze_single_period_for_continuous(current_period, train_data)
                if result:
                    result['sequence'] = i + 1
                    analysis_results.append(result)
        
        print(f"\n连续分析演示完成！共分析了 {len(analysis_results)} 期")

        # 演示保存功能
        if analysis_results:
            print("演示保存功能...")
            success = analyzer.save_continuous_analysis_results(analysis_results)
            if success:
                print("Excel文件保存成功！")
            else:
                print("Excel文件保存失败！")
        
    return True

def main():
    """
    主演示函数
    """
    print("彩票数据分析增强版程序 - 功能演示")
    print("=" * 80)
    
    # 演示单期分析
    demo_single_analysis()
    
    # 演示连续分析
    demo_continuous_analysis()
    
    print("\n" + "=" * 80)
    print("演示完成！")
    print("=" * 80)
    print("\n程序功能说明：")
    print("1. 支持SSQ（双色球）和DLT（大乐透）两种彩票类型")
    print("2. 提供详细的历史数据统计分析（奇偶比、质合比、大小比、和值走势）")
    print("3. 单期分析：分析指定期号的历史概率分布和跟随性概率分布")
    print("4. 连续分析：从指定期号开始连续分析最多500期，结果保存到Excel")
    print("5. 模块化设计，便于后续扩展和修改")
    print("\n要运行完整的交互式程序，请执行：python lottery_analysis_enhanced.py")

if __name__ == "__main__":
    main()
