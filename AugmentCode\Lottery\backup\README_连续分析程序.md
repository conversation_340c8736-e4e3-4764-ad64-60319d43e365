# 彩票连续分析程序使用说明

## 程序概述

本程序基于 `Lottery_Final.py` 的核心功能重新开发，实现了彩票连续分析功能。程序能够从指定期号开始连续分析最多500期的彩票数据，并将分析结果保存到Word文档中。

## 主要功能

### 1. 彩票类型选择
- 支持双色球（SSQ）和大乐透（DLT）两种彩票类型
- 自动读取相应的Excel数据表格

### 2. 连续分析功能
- 用户指定开始分析的期号（如25001）
- 程序从该期号开始连续分析，最多分析500期
- 每期分析使用该期之前的所有数据作为训练数据

### 3. 分析模块（模块化设计）

#### 模块1：历史出现概率分布分析
- 统计所有红球与蓝球号码的历史出现概率分布（百分比显示）
- 给出分析期号的红蓝球号码的相关概率
- 分析分析期号的红球历史出现概率在所有红球历史出现概率分布的置信区间
- 分析分析期号的蓝球历史出现概率在所有蓝球历史出现概率分布的置信区间

#### 模块2：跟随性概率分布分析
- 基于分析期号的上一期号码计算多条件贝叶斯预测概率
- 统计所有红球与蓝球号码的跟随性概率分布（百分比显示）
- 给出分析期号的红蓝球号码的相关跟随性概率
- 分析分析期号的红球跟随性概率在所有红球号码跟随性概率分布的置信区间
- 分析分析期号的蓝球跟随性概率在所有蓝球号码跟随性概率分布的置信区间

### 4. Word文档输出
- 将所有分析结果保存到Word文档中
- 包含详细的概率分布、置信区间分析等信息
- 文档格式清晰，便于阅读和分析

## 文件结构

```
AugmentCode/Lottery/
├── lottery_continuous_analysis.py    # 主程序文件
├── test_continuous_analysis.py       # 测试程序
├── lottery_data_all.xlsx            # 原始数据文件
└── README_连续分析程序.md            # 使用说明
```

## 使用方法

### 1. 环境准备
确保安装了以下Python包：
```bash
pip install pandas numpy python-docx scipy openpyxl
```

### 2. 数据准备
确保 `lottery_data_all.xlsx` 文件存在，包含以下工作表：
- `SSQ_data_all`：双色球数据（A列期号 + I-O列号码）
- `DLT_data_all`：大乐透数据（A列期号 + H-N列号码）

### 3. 运行程序
```bash
python lottery_continuous_analysis.py
```

### 4. 操作步骤
1. **选择彩票类型**：输入1选择SSQ（双色球），输入2选择DLT（大乐透）
2. **输入开始期号**：输入要开始分析的期号（如25001）
3. **等待分析完成**：程序会自动进行连续分析
4. **查看结果**：分析完成后会生成Word文档

## 输出文件

程序会生成以下格式的Word文档：
```
lottery_continuous_analysis_SSQ_20241214_143022.docx
```

文件名格式：`lottery_continuous_analysis_{彩票类型}_{时间戳}.docx`

## 分析结果说明

### Word文档内容结构
1. **分析概况**：彩票类型、分析期数、分析时间
2. **每期分析详情**：
   - 期号信息
   - 实际号码和上期号码
   - 历史出现概率分布分析
   - 跟随性概率分布分析

### 置信区间分析
- 使用95%置信水平
- 分析结果包括：
  - "低于95%置信区间下界"
  - "在95%置信区间内"
  - "高于95%置信区间上界"

## 程序特点

### 1. 模块化设计
- 分析功能模块化，便于后续添加或删除新的分析内容
- 每个分析模块独立，互不影响

### 2. 数据处理
- 沿用 `Lottery_Final.py` 中的核心数据处理逻辑
- 支持SSQ和DLT两种彩票类型的不同数据格式

### 3. 统计分析
- 基于贝叶斯概率理论
- 包含置信区间分析
- 提供详细的概率分布信息

### 4. 用户友好
- 清晰的用户交互界面
- 详细的进度提示
- 完整的错误处理

## 技术实现

### 核心算法
1. **历史概率计算**：统计各球号在历史数据中的出现频率
2. **跟随性概率计算**：基于多条件贝叶斯方法计算球号跟随概率
3. **置信区间计算**：使用正态分布理论计算95%置信区间

### 数据结构
- 使用pandas DataFrame处理Excel数据
- 使用numpy进行数值计算
- 使用python-docx生成Word文档

## 注意事项

1. **数据要求**：确保Excel文件格式正确，数据完整
2. **内存使用**：大量数据分析可能占用较多内存
3. **运行时间**：分析500期数据可能需要几分钟时间
4. **文件权限**：确保有写入Word文档的权限

## 扩展功能

程序采用模块化设计，可以轻松添加新的分析模块：

1. 在 `LotteryContinuousAnalyzer` 类中添加新的分析方法
2. 在 `continuous_analysis` 方法中调用新模块
3. 在 `save_results_to_word` 方法中添加新模块的输出格式

## 故障排除

### 常见问题
1. **找不到文件**：检查 `lottery_data_all.xlsx` 是否存在
2. **期号不存在**：确认输入的期号在数据范围内
3. **内存不足**：减少分析期数或增加系统内存
4. **依赖包缺失**：运行 `pip install` 命令安装所需包

### 联系支持
如遇到问题，请检查：
1. Python版本（建议3.8+）
2. 依赖包版本
3. 数据文件格式
4. 系统权限设置
